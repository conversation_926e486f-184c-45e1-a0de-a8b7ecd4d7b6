using System;
using System.Collections.Generic;
using System.Linq;
using Oxide.Core;
using Oxide.Core.Plugins;
using Oxide.Game.Rust.Cui;
using UnityEngine;
using Newtonsoft.Json;

namespace Oxide.Plugins
{
    [Info("Electric Vehicles", "YourName", "1.0.0")]
    [Description("Adds electric power to vehicles with charging in TC range")]
    class EVehicles : RustPlugin
    {
        #region Fields
        private Configuration config;
        private Dictionary<ulong, Dictionary<uint, VehicleData>> playerVehicles = new Dictionary<ulong, Dictionary<uint, VehicleData>>();
        private Dictionary<uint, GameObject> batteryObjects = new Dictionary<uint, GameObject>();
        private Dictionary<uint, GameObject> chargeEffects = new Dictionary<uint, GameObject>();
        private Dictionary<uint, Timer> drainTimers = new Dictionary<uint, Timer>();
        private Dictionary<uint, Timer> chargeTimers = new Dictionary<uint, Timer>();
        private Dictionary<ulong, string> hudPanels = new Dictionary<ulong, string>();
        private const string HudPanelName = "EVehiclesHUD";

        // Permission constants
        private const string PermissionUse = "evehicles.use";
        private const string PermissionAdmin = "evehicles.admin";
        private const string PermissionVIP = "evehicles.vip";
        #endregion

        #region Classes
        private class VehicleData
        {
            public float CurrentCharge { get; set; }
            public float MaxCharge { get; set; }
            public float ChargeRate { get; set; }
            public float DrainRate { get; set; }
            public string DisplayName { get; set; }
            public bool HasBattery { get; set; }
            public List<BatteryPosition> BatteryPositions { get; set; }
            public bool IsCharging { get; set; }
            public float LastNotifiedCharge { get; set; }
        }

        private class BatteryPosition
        {
            public float X { get; set; }
            public float Y { get; set; }
            public float Z { get; set; }
            public float RotationX { get; set; }
            public float RotationY { get; set; }
            public float RotationZ { get; set; }
        }
        #endregion

        #region Configuration
        private class Configuration
        {
            [JsonProperty("Show Charge Capacity on mounted")]
            public bool ShowChargeOnMount { get; set; }

            [JsonProperty("Show Charge Capacity on dismounted")]
            public bool ShowChargeOnDismount { get; set; }

            [JsonProperty("Show Charge Refil Notify Always")]
            public bool ShowChargeRefilAlways { get; set; }

            [JsonProperty("Show Charge Refil Notify only when Mounted")]
            public bool ShowChargeRefilMounted { get; set; }

            [JsonProperty("Notify each Refil Amount")]
            public float NotifyRefilAmount { get; set; }

            [JsonProperty("Warning Low Electric")]
            public float WarningLowElectric { get; set; }

            [JsonProperty("Enable Low Electric Warning Sound")]
            public bool EnableLowWarningSound { get; set; }

            [JsonProperty("Low Electric Warning Sound (effect prefab)")]
            public string LowWarningSound { get; set; }

            [JsonProperty("Enable Fully Charged Sound")]
            public bool EnableFullyChargedSound { get; set; }

            [JsonProperty("Fully Charged Sound (effect prefab)")]
            public string FullyChargedSound { get; set; }

            [JsonProperty("Show Charge Effect (on battery)")]
            public bool ShowChargeEffectBattery { get; set; }

            [JsonProperty("Show Charge Effect when no battery")]
            public bool ShowChargeEffectNoBattery { get; set; }

            [JsonProperty("Charging Effect (effect prefab)")]
            public string ChargingEffect { get; set; }

            [JsonProperty("Show HUD Icon")]
            public bool ShowHudIcon { get; set; }

            [JsonProperty("HUD Icon Default X")]
            public float HudIconX { get; set; }

            [JsonProperty("HUD Icon Default Y")]
            public float HudIconY { get; set; }

            [JsonProperty("Electric Vehicle List")]
            public List<ElectricVehicle> ElectricVehicles { get; set; }
        }

        private class ElectricVehicle
        {
            [JsonProperty("Enable this Vehicle")]
            public bool Enabled { get; set; }

            [JsonProperty("Vehicle Prefab Path")]
            public string PrefabPath { get; set; }

            [JsonProperty("Vehicle Display Name")]
            public string DisplayName { get; set; }

            [JsonProperty("Start Electric Capacity")]
            public float StartCapacity { get; set; }

            [JsonProperty("Maximum Electric Capacity")]
            public float MaxCapacity { get; set; }

            [JsonProperty("Electric Charging per sec")]
            public float ChargingRate { get; set; }

            [JsonProperty("Electric Drain per sec")]
            public float DrainRate { get; set; }

            [JsonProperty("Place Battery")]
            public bool PlaceBattery { get; set; }

            [JsonProperty("Battery Positions")]
            public List<BatteryPosition> BatteryPositions { get; set; }
        }
        #endregion

        #region Oxide Hooks
        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
            }
            catch
            {
                PrintError("Failed to load config, creating new one");
                LoadDefaultConfig();
            }
            SaveConfig();
        }

        protected override void LoadDefaultConfig()
        {
            config = new Configuration
            {
                ShowChargeOnMount = true,
                ShowChargeOnDismount = false,
                ShowChargeRefilAlways = false,
                ShowChargeRefilMounted = true,
                NotifyRefilAmount = 100.0f,
                WarningLowElectric = 100.0f,
                EnableLowWarningSound = true,
                LowWarningSound = "assets/prefabs/locks/keypad/effects/lock.code.denied.prefab",
                EnableFullyChargedSound = true,
                FullyChargedSound = "assets/prefabs/locks/keypad/effects/lock.code.updated.prefab",
                ShowChargeEffectBattery = true,
                ShowChargeEffectNoBattery = false,
                ChargingEffect = "assets/prefabs/locks/keypad/effects/lock.code.shock.prefab",
                ShowHudIcon = true,
                HudIconX = 606.0f,
                HudIconY = 84.0f,
                ElectricVehicles = new List<ElectricVehicle>
                {
                    new ElectricVehicle
                    {
                        Enabled = true,
                        PrefabPath = "assets/content/vehicles/minicopter/minicopter.entity.prefab",
                        DisplayName = "Miny",
                        StartCapacity = 500.0f, // Changed from 0.0f to 500.0f (full charge)
                        MaxCapacity = 500.0f,
                        ChargingRate = 1.0f,
                        DrainRate = 2.0f,
                        PlaceBattery = true,
                        BatteryPositions = new List<BatteryPosition>
                        {
                            new BatteryPosition
                            {
                                X = 0.0f,
                                Y = 0.75f,
                                Z = -0.4f,
                                RotationX = -5.0f,
                                RotationY = 180.0f,
                                RotationZ = 0.0f
                            }
                        }
                    }
                }
            };
        }

        protected override void SaveConfig() => Config.WriteObject(config);

        void OnServerInitialized()
        {
            // Register permissions
            permission.RegisterPermission(PermissionUse, this);
            permission.RegisterPermission(PermissionAdmin, this);
            permission.RegisterPermission(PermissionVIP, this);
            
            // Register commands
            cmd.AddChatCommand("evehicle", this, "CmdEVehicle");
            cmd.AddChatCommand("evdebug", this, "CmdEVDebug");
            
            // Find existing vehicles
            foreach (var vehicle in BaseNetworkable.serverEntities.OfType<BaseVehicle>())
            {
                CheckAndSetupVehicle(vehicle);
            }
            
            Puts("Electric Vehicles plugin initialized!");
        }

        void Unload()
        {
            // Clean up timers
            foreach (var timer in drainTimers.Values)
                timer?.Destroy();
            
            foreach (var timer in chargeTimers.Values)
                timer?.Destroy();
            
            // Clean up battery objects
            foreach (var battery in batteryObjects.Values)
                UnityEngine.Object.Destroy(battery);
            
            // Clean up charge effects
            foreach (var effect in chargeEffects.Values)
                UnityEngine.Object.Destroy(effect);
            
            // Clean up UI
            foreach (var player in BasePlayer.activePlayerList)
                CuiHelper.DestroyUi(player, HudPanelName);
        }

        void OnEntitySpawned(BaseNetworkable entity)
        {
            if (entity is BaseVehicle vehicle)
                CheckAndSetupVehicle(vehicle);
        }

        void OnEntityKill(BaseNetworkable entity)
        {
            if (entity is BaseVehicle vehicle)
                CleanupVehicle((uint)vehicle.net.ID.Value);
        }

        void OnPlayerConnected(BasePlayer player)
        {
            if (config.ShowHudIcon)
                CreateHUD(player);
        }

        void OnPlayerDisconnected(BasePlayer player)
        {
            CuiHelper.DestroyUi(player, HudPanelName);
        }

        object OnEntityMounted(BaseMountable mountable, BasePlayer player)
        {
            if (mountable.VehicleParent() is BaseVehicle vehicle)
            {
                uint vehicleID = (uint)vehicle.net.ID.Value;
                
                // Check if this is an electric vehicle
                if (IsElectricVehicle(vehicle) && IsDriver(player, vehicle))
                {
                    // Check if player has permission
                    if (!permission.UserHasPermission(player.UserIDString, PermissionUse) && 
                        !permission.UserHasPermission(player.UserIDString, PermissionVIP) && 
                        !permission.UserHasPermission(player.UserIDString, PermissionAdmin))
                    {
                        SendMessage(player, "You don't have permission to use electric vehicles.");
                        return null;
                    }
                    
                    // Initialize vehicle data if first time
                    InitializeVehicleForPlayer(player.userID, vehicle);
                    
                    // Modify vehicle to use electric power
                    ModifyVehicleForElectric(vehicle);
                    
                    // Start drain timer
                    StartDrainTimer(vehicle, player);
                    
                    // Show charge message
                    if (config.ShowChargeOnMount)
                        ShowChargeMessage(player, vehicleID);
                    
                    // Update HUD
                    if (config.ShowHudIcon)
                        UpdateHUD(player);
                    
                    SendMessage(player, $"You are now driving an electric vehicle. Use /evehicle for info.");
                }
            }
            return null;
        }

        object OnEntityDismounted(BaseMountable mountable, BasePlayer player)
        {
            if (mountable.VehicleParent() is BaseVehicle vehicle)
            {
                uint vehicleID = (uint)vehicle.net.ID.Value;
                
                // Check if this is an electric vehicle
                if (IsElectricVehicle(vehicle) && IsDriver(player, vehicle))
                {
                    // Stop drain timer
                    StopDrainTimer(vehicleID);
                    
                    // Show charge message
                    if (config.ShowChargeOnDismount)
                        ShowChargeMessage(player, vehicleID);
                    
                    // Update HUD
                    if (config.ShowHudIcon)
                        UpdateHUD(player);
                }
            }
            return null;
        }
        #endregion

        #region Core Methods
        private void CheckAndSetupVehicle(BaseVehicle vehicle)
        {
            if (!IsElectricVehicle(vehicle))
                return;
                
            ElectricVehicle vehicleConfig = GetVehicleConfig(vehicle);
            if (vehicleConfig == null || !vehicleConfig.Enabled)
                return;
            
            // Check if vehicle has an owner and if they have permission
            if (vehicle.OwnerID != 0)
            {
                string ownerIdString = vehicle.OwnerID.ToString();
                if (!permission.UserHasPermission(ownerIdString, PermissionUse) && 
                    !permission.UserHasPermission(ownerIdString, PermissionVIP) && 
                    !permission.UserHasPermission(ownerIdString, PermissionAdmin))
                {
                    return;
                }
            }
                
            // Add battery if needed
            if (vehicleConfig.PlaceBattery && vehicleConfig.BatteryPositions.Count > 0)
            {
                AddBatteryToVehicle(vehicle, vehicleConfig.BatteryPositions[0]);
            }
            
            // Start charging timer if in TC range
            CheckAndStartCharging(vehicle);
            
            Puts($"Set up electric vehicle: {vehicleConfig.DisplayName} (ID: {vehicle.net.ID.Value})");
        }

        private void InitializeVehicleForPlayer(ulong playerID, BaseVehicle vehicle)
        {
            uint vehicleID = (uint)vehicle.net.ID.Value;
            ElectricVehicle vehicleConfig = GetVehicleConfig(vehicle);
            
            if (vehicleConfig == null)
                return;
                
            if (!playerVehicles.ContainsKey(playerID))
                playerVehicles[playerID] = new Dictionary<uint, VehicleData>();
                
            if (!playerVehicles[playerID].ContainsKey(vehicleID))
            {
                // Set initial charge to max capacity (100%)
                playerVehicles[playerID][vehicleID] = new VehicleData
                {
                    CurrentCharge = vehicleConfig.MaxCapacity,
                    MaxCharge = vehicleConfig.MaxCapacity,
                    ChargeRate = vehicleConfig.ChargingRate,
                    DrainRate = vehicleConfig.DrainRate,
                    DisplayName = vehicleConfig.DisplayName,
                    HasBattery = vehicleConfig.PlaceBattery,
                    BatteryPositions = vehicleConfig.BatteryPositions,
                    IsCharging = false,
                    LastNotifiedCharge = 0f
                };
            }
        }

        private void StartDrainTimer(BaseVehicle vehicle, BasePlayer driver)
        {
            uint vehicleID = (uint)vehicle.net.ID.Value;
            ulong playerID = driver.userID;
            
            // Stop existing timer if any
            StopDrainTimer(vehicleID);
            
            // Start new timer
            drainTimers[vehicleID] = timer.Every(1f, () => {
                if (!playerVehicles.ContainsKey(playerID) || !playerVehicles[playerID].ContainsKey(vehicleID))
                    return;
                    
                VehicleData data = playerVehicles[playerID][vehicleID];
                
                // Drain power
                data.CurrentCharge -= data.DrainRate;
                
                // Check if out of power
                if (data.CurrentCharge <= 0)
                {
                    data.CurrentCharge = 0;
                    
                    // For minicopters and scrap helis, set fuel consumption back to normal
                    if (vehicle is Minicopter minicopter)
                    {
                        var settings = vehicle.gameObject.GetComponent<OriginalVehicleSettings>();
                        if (settings != null)
                        {
                            minicopter.fuelPerSec = settings.originalFuelPerSec;
                        }
                        
                        // Remove fuel to stop the vehicle
                        var fuelSystem = vehicle.GetFuelSystem();
                        if (fuelSystem != null)
                        {
                            fuelSystem.AddFuel(-fuelSystem.GetFuelAmount()); // Remove all fuel
                        }
                    }
                    else if (vehicle is ScrapTransportHelicopter scrapHeli)
                    {
                        var settings = vehicle.gameObject.GetComponent<OriginalVehicleSettings>();
                        if (settings != null)
                        {
                            scrapHeli.fuelPerSec = settings.originalFuelPerSec;
                        }
                        
                        // Remove fuel to stop the vehicle
                        var fuelSystem = vehicle.GetFuelSystem();
                        if (fuelSystem != null)
                        {
                            fuelSystem.AddFuel(-fuelSystem.GetFuelAmount()); // Remove all fuel
                        }
                    }
                    else
                    {
                        // For other vehicles, just remove fuel
                        var fuelSystem = vehicle.GetFuelSystem();
                        if (fuelSystem != null)
                        {
                            fuelSystem.AddFuel(-fuelSystem.GetFuelAmount()); // Remove all fuel
                        }
                    }
                    
                    SendMessage(driver, $"Your {data.DisplayName} has run out of power!");
                    StopDrainTimer(vehicleID);
                    return;
                }
                
                // Check for low power warning
                if (data.CurrentCharge <= config.WarningLowElectric && config.EnableLowWarningSound)
                {
                    PlaySound(driver, config.LowWarningSound);
                    SendMessage(driver, $"Warning: {data.DisplayName} is low on power! ({data.CurrentCharge:0}/{data.MaxCharge:0})");
                }
                
                // Update HUD
                if (config.ShowHudIcon)
                    UpdateHUD(driver);
            });
        }

        private void StopDrainTimer(uint vehicleID)
        {
            if (drainTimers.ContainsKey(vehicleID))
            {
                drainTimers[vehicleID]?.Destroy();
                drainTimers.Remove(vehicleID);
            }
        }

        private void CheckAndStartCharging(BaseVehicle vehicle)
        {
            uint vehicleID = (uint)vehicle.net.ID.Value;
            
            // Check if in TC range
            BuildingPrivlidge tc = GetNearestTC(vehicle.transform.position);
            if (tc == null)
                return;
                
            // Get owner ID from TC
            ulong ownerID = tc.OwnerID;
            if (ownerID == 0)
                return;
                
            // Initialize vehicle data if needed
            InitializeVehicleForPlayer(ownerID, vehicle);
            
            // Start charging timer - FIXED: explicit cast from ulong to uint
            StartChargingTimer(vehicle, ownerID);
        }

        private void StartChargingTimer(BaseVehicle vehicle, ulong ownerID)
        {
            uint vehicleID = (uint)vehicle.net.ID.Value;
            
            // Stop existing timer if any
            StopChargingTimer(vehicleID);
            
            if (!playerVehicles.ContainsKey(ownerID) || !playerVehicles[ownerID].ContainsKey(vehicleID))
                return;
                
            VehicleData data = playerVehicles[ownerID][vehicleID];
            data.IsCharging = true;
            
            // Start charging effect
            if ((data.HasBattery && config.ShowChargeEffectBattery) || 
                (!data.HasBattery && config.ShowChargeEffectNoBattery))
            {
                CreateChargingEffect(vehicle);
            }
            
            // Start charging timer
            chargeTimers[vehicleID] = timer.Every(1f, () => {
                if (!playerVehicles.ContainsKey(ownerID) || !playerVehicles[ownerID].ContainsKey(vehicleID))
                    return;
                    
                // Check if still in TC range
                BuildingPrivlidge tc = GetNearestTC(vehicle.transform.position);
                if (tc == null || tc.OwnerID != ownerID)
                {
                    StopChargingTimer(vehicleID);
                    return;
                }
                
                // Charge vehicle
                float previousCharge = data.CurrentCharge;
                data.CurrentCharge = Math.Min(data.CurrentCharge + data.ChargeRate, data.MaxCharge);
                
                // Check if fully charged
                if (data.CurrentCharge >= data.MaxCharge && previousCharge < data.MaxCharge)
                {
                    data.CurrentCharge = data.MaxCharge;
                    
                    // Play fully charged sound
                    if (config.EnableFullyChargedSound)
                    {
                        BasePlayer player = BasePlayer.FindByID(ownerID);
                        if (player != null)
                        {
                            PlaySound(player, config.FullyChargedSound);
                            SendMessage(player, $"Your {data.DisplayName} is now fully charged!");
                        }
                    }
                }
                
                // Check if we should notify about charge increase
                if (config.ShowChargeRefilAlways || 
                    (config.ShowChargeRefilMounted && GetVehicleDriver(vehicle) != null))
                {
                    if (data.CurrentCharge - data.LastNotifiedCharge >= config.NotifyRefilAmount)
                    {
                        data.LastNotifiedCharge = data.CurrentCharge;
                        
                        BasePlayer player = BasePlayer.FindByID(ownerID);
                        if (player != null)
                        {
                            SendMessage(player, $"Your {data.DisplayName} is charging: {data.CurrentCharge:0}/{data.MaxCharge:0}");
                        }
                    }
                }
                
                // Update HUD for driver if any
                BasePlayer driver = GetVehicleDriver(vehicle);
                if (driver != null && config.ShowHudIcon)
                {
                    UpdateHUD(driver);
                }
            });
        }

        private void StopChargingTimer(uint vehicleID)
        {
            if (chargeTimers.ContainsKey(vehicleID))
            {
                chargeTimers[vehicleID]?.Destroy();
                chargeTimers.Remove(vehicleID);
            }
            
            // Stop charging effect
            RemoveChargingEffect(vehicleID);
            
            // Update charging status
            foreach (var playerData in playerVehicles.Values)
            {
                if (playerData.ContainsKey(vehicleID))
                {
                    playerData[vehicleID].IsCharging = false;
                }
            }
        }

        private void CleanupVehicle(uint vehicleID)
        {
            StopDrainTimer(vehicleID);
            StopChargingTimer(vehicleID);
            
            // Remove battery
            if (batteryObjects.ContainsKey(vehicleID))
            {
                UnityEngine.Object.Destroy(batteryObjects[vehicleID]);
                batteryObjects.Remove(vehicleID);
            }
            
            // Remove from player data
            foreach (var playerData in playerVehicles.Values)
            {
                playerData.Remove(vehicleID);
            }
        }
        #endregion

        #region Helper Methods
        private bool IsElectricVehicle(BaseVehicle vehicle)
        {
            return GetVehicleConfig(vehicle) != null;
        }

        private ElectricVehicle GetVehicleConfig(BaseVehicle vehicle)
        {
            if (vehicle == null) return null;
            
            string prefabPath = vehicle.PrefabName;
            return config.ElectricVehicles.FirstOrDefault(v => v.Enabled && v.PrefabPath == prefabPath);
        }

        private void AddBatteryToVehicle(BaseVehicle vehicle, BatteryPosition position)
        {
            uint vehicleID = (uint)vehicle.net.ID.Value;
            
            // Clean up existing battery if any
            if (batteryObjects.ContainsKey(vehicleID))
            {
                UnityEngine.Object.Destroy(batteryObjects[vehicleID]);
                batteryObjects.Remove(vehicleID);
            }
            
            // Create battery model - using a valid prefab path for Rust
            GameObject battery = GameManager.server.CreatePrefab("assets/prefabs/deployable/small oil refinery/small_refinery_deployed.prefab", 
                                                               vehicle.transform.position, Quaternion.identity, true);
            
            if (battery != null)
            {
                // Set parent and position
                battery.transform.SetParent(vehicle.transform);
                battery.transform.localPosition = new Vector3(position.X, position.Y, position.Z);
                battery.transform.localRotation = Quaternion.Euler(position.RotationX, position.RotationY, position.RotationZ);
                battery.transform.localScale = new Vector3(0.3f, 0.3f, 0.3f); // Smaller scale for refinery
                
                // Disable collider
                Collider[] colliders = battery.GetComponentsInChildren<Collider>();
                foreach (var collider in colliders)
                    collider.enabled = false;
                
                // Store reference
                batteryObjects[vehicleID] = battery;
            }
            else
            {
                Puts("Failed to create battery object - prefab not found");
            }
        }

        private void CreateChargingEffect(BaseVehicle vehicle)
        {
            uint vehicleID = (uint)vehicle.net.ID.Value;
            if (chargeEffects.ContainsKey(vehicleID))
            {
                RemoveChargingEffect(vehicleID);
            }

            GameObject effect = GameManager.server.CreatePrefab(config.ChargingEffect, vehicle.transform.position, Quaternion.identity, true);
            if (effect != null)
            {
                effect.transform.SetParent(vehicle.transform);
                effect.transform.localPosition = Vector3.zero;
                effect.transform.localRotation = Quaternion.identity;
                chargeEffects[vehicleID] = effect;
            }
        }

        private void RemoveChargingEffect(uint vehicleID)
        {
            if (chargeEffects.ContainsKey(vehicleID))
            {
                UnityEngine.Object.Destroy(chargeEffects[vehicleID]);
                chargeEffects.Remove(vehicleID);
            }
        }
        #endregion

        private void CreateHUD(BasePlayer player)
        {
            if (player == null) return;
            
            // Destroy existing HUD if any
            CuiHelper.DestroyUi(player, HudPanelName);
            
            // Create new HUD container
            var container = new CuiElementContainer();
            
            // Add main panel
            container.Add(new CuiPanel
            {
                RectTransform = { 
                    AnchorMin = "0 0", 
                    AnchorMax = "0 0", 
                    OffsetMin = $"{config.HudIconX} {config.HudIconY}", 
                    OffsetMax = $"{config.HudIconX + 150} {config.HudIconY + 30}" 
                },
                Image = { Color = "0.1 0.1 0.1 0.8" }
            }, "Hud", HudPanelName);
            
            // Add label
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                Text = { 
                    Text = "Electric: N/A", 
                    FontSize = 12, 
                    Align = TextAnchor.MiddleCenter, 
                    Color = "1 1 1 1" 
                }
            }, HudPanelName, HudPanelName + "_Label");
            
            // Add HUD to player
            CuiHelper.AddUi(player, container);
            
            // Store panel ID
            hudPanels[player.userID] = HudPanelName;
        }
        
        private void UpdateHUD(BasePlayer player)
        {
            if (player == null) return;
            
            // Find vehicle the player is driving
            BaseVehicle vehicle = GetVehiclePlayerIsDriving(player);
            if (vehicle == null) 
            {
                // No vehicle, hide HUD
                CuiHelper.DestroyUi(player, HudPanelName);
                return;
            }
            
            // FIXED: Get vehicle ID as uint
            uint vehicleID = (uint)vehicle.net.ID.Value;
            
            // Check if we have data for this vehicle
            if (!playerVehicles.ContainsKey(player.userID) || !playerVehicles[player.userID].ContainsKey(vehicleID))
                return;
                
            VehicleData data = playerVehicles[player.userID][vehicleID];
            
            // Update HUD text
            string status = data.IsCharging ? "Charging" : "Discharging";
            string text = $"{data.DisplayName}: {data.CurrentCharge:0}/{data.MaxCharge:0} ({status})";
            
            // Update the label
            CuiHelper.DestroyUi(player, HudPanelName + "_Label");
            
            var element = new CuiElement
            {
                Parent = HudPanelName,
                Name = HudPanelName + "_Label",
                Components = 
                {
                    new CuiTextComponent
                    {
                        Text = text,
                        FontSize = 12,
                        Align = TextAnchor.MiddleCenter,
                        Color = data.CurrentCharge < config.WarningLowElectric ? "1 0.3 0.3 1" : "1 1 1 1"
                    },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0 0",
                        AnchorMax = "1 1"
                    }
                }
            };
            
            var container = new CuiElementContainer();
            container.Add(element);
            
            CuiHelper.AddUi(player, container);
        }
        
        private BaseVehicle GetVehiclePlayerIsDriving(BasePlayer player)
        {
            if (player == null) return null;
            
            BaseMountable mountable = player.GetMounted();
            if (mountable == null) return null;
            
            BaseVehicle vehicle = mountable.VehicleParent();
            if (vehicle == null) return null;
            
            // Check if player is in driver's seat
            if (!IsDriver(player, vehicle)) return null;
            
            return vehicle;
        }
        
        private bool IsDriver(BasePlayer player, BaseVehicle vehicle)
        {
            if (player == null || vehicle == null) return false;
            
            // Check if player is mounted on the vehicle
            BaseMountable mountable = player.GetMounted();
            if (mountable == null) return false;
            
            // Check if the mountable belongs to this vehicle
            if (mountable.VehicleParent() != vehicle) return false;
            
            // Check if it's the driver's seat (usually the first mount point)
            if (vehicle.mountPoints.Count > 0 && vehicle.mountPoints[0].mountable == mountable)
                return true;
                
            return false;
        }
        
        private bool IsPlayerDrivingVehicle(BasePlayer player, BaseVehicle vehicle)
        {
            return IsDriver(player, vehicle);
        }
        
        private BasePlayer GetVehicleDriver(BaseVehicle vehicle)
        {
            if (vehicle == null || vehicle.mountPoints.Count == 0) return null;
            
            // Check the driver's seat (usually the first mount point)
            BaseMountable driverSeat = vehicle.mountPoints[0].mountable;
            if (driverSeat == null) return null;
            
            return driverSeat.GetMounted();
        }
        
        private BuildingPrivlidge GetNearestTC(Vector3 position)
        {
            BuildingPrivlidge result = null;
            float closestDistance = 30f; // Maximum range to check for TC
            
            try {
                // Use a safer way to find TCs
                List<BuildingPrivlidge> tcs = UnityEngine.Object.FindObjectsOfType<BuildingPrivlidge>().ToList();
                
                if (tcs == null || tcs.Count == 0)
                {
                    Puts("No building privileges found in the world");
                    return null;
                }
                
                foreach (BuildingPrivlidge tc in tcs)
                {
                    if (tc == null) continue;
                    
                    float distance = Vector3.Distance(position, tc.transform.position);
                    if (distance < closestDistance)
                    {
                        closestDistance = distance;
                        result = tc;
                    }
                }
            }
            catch (Exception ex) {
                Puts($"Error in GetNearestTC: {ex.Message}");
            }
            
            return result;
        }
        
        private void ShowChargeMessage(BasePlayer player, uint vehicleID)
        {
            if (player == null) return;
            
            // Check if we have data for this vehicle
            if (!playerVehicles.ContainsKey(player.userID) || !playerVehicles[player.userID].ContainsKey(vehicleID))
                return;
                
            VehicleData data = playerVehicles[player.userID][vehicleID];
            
            // Show message
            SendMessage(player, $"{data.DisplayName} electric charge: {data.CurrentCharge:0}/{data.MaxCharge:0}");
        }
        
        private void SendMessage(BasePlayer player, string message)
        {
            if (player == null) return;
            
            player.ChatMessage(message);
        }
        
        private void PlaySound(BasePlayer player, string soundPrefab)
        {
            if (player == null) return;
            
            Effect.server.Run(soundPrefab, player.transform.position);
        }
        
        private void CmdEVehicle(BasePlayer player, string command, string[] args)
        {
            if (player == null) return;
            
            // Check if player is in a vehicle
            BaseVehicle vehicle = GetVehiclePlayerIsDriving(player);
            if (vehicle == null)
            {
                SendMessage(player, "You must be driving an electric vehicle to use this command.");
                return;
            }
            
            uint vehicleID = Convert.ToUInt32(vehicle.net.ID.Value);
            
            // Check if this is an electric vehicle
            if (!IsElectricVehicle(vehicle))
            {
                SendMessage(player, "This is not an electric vehicle.");
                return;
            }
            
            // Show vehicle info
            if (!playerVehicles.ContainsKey(player.userID) || !playerVehicles[player.userID].ContainsKey(vehicleID))
            {
                SendMessage(player, "No data available for this vehicle.");
                return;
            }
            
            VehicleData data = playerVehicles[player.userID][vehicleID];
            
            SendMessage(player, $"Vehicle: {data.DisplayName}");
            SendMessage(player, $"Charge: {data.CurrentCharge:0}/{data.MaxCharge:0}");
            SendMessage(player, $"Charging Rate: {data.ChargeRate}/sec");
            SendMessage(player, $"Drain Rate: {data.DrainRate}/sec");
            SendMessage(player, $"Status: {(data.IsCharging ? "Charging" : "Not Charging")}");
        }

        [ChatCommand("evdebug")]
        private void CmdEVDebug(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, PermissionAdmin))
            {
                SendMessage(player, "You don't have permission to use this command.");
                return;
            }
            
            SendMessage(player, "Electric Vehicles Debug Info:");
            SendMessage(player, $"Total tracked vehicles: {playerVehicles.Sum(p => p.Value.Count)}");
            SendMessage(player, $"Battery objects: {batteryObjects.Count}");
            SendMessage(player, $"Charge effects: {chargeEffects.Count}");
            SendMessage(player, $"Drain timers: {drainTimers.Count}");
            SendMessage(player, $"Charge timers: {chargeTimers.Count}");
            
            // Check current vehicle
            BaseVehicle vehicle = GetVehiclePlayerIsDriving(player);
            if (vehicle != null)
            {
                uint vehicleID = (uint)vehicle.net.ID.Value;
                SendMessage(player, $"Current vehicle ID: {vehicleID}");
                SendMessage(player, $"Is electric: {IsElectricVehicle(vehicle)}");
                
                if (playerVehicles.ContainsKey(player.userID) && playerVehicles[player.userID].ContainsKey(vehicleID))
                {
                    VehicleData data = playerVehicles[player.userID][vehicleID];
                    SendMessage(player, $"Vehicle data: {data.DisplayName}, Charge: {data.CurrentCharge}/{data.MaxCharge}");
                    SendMessage(player, $"Is charging: {data.IsCharging}, Drain rate: {data.DrainRate}, Charge rate: {data.ChargeRate}");
                }
                else
                {
                    SendMessage(player, "No vehicle data found for current vehicle.");
                }
            }
            else
            {
                SendMessage(player, "You are not driving a vehicle.");
            }
        }
    }
}

// Add this method to your class
private void ModifyVehicleForElectric(BaseVehicle vehicle)
{
    // For minicopters
    if (vehicle is Minicopter minicopter)
    {
        // Save original values if needed
        if (!vehicle.gameObject.HasComponent<OriginalVehicleSettings>())
        {
            var settings = vehicle.gameObject.AddComponent<OriginalVehicleSettings>();
            settings.originalFuelPerSec = minicopter.fuelPerSec;
        }
        
        // Set fuel consumption to zero
        minicopter.fuelPerSec = 0f;
        
        // Add a small amount of fuel to keep it running
        var fuelSystem = vehicle.GetFuelSystem();
        if (fuelSystem != null)
        {
            fuelSystem.AddFuel(10);
        }
    }
    
    // For scrap helicopters
    else if (vehicle is ScrapTransportHelicopter scrapHeli)
    {
        // Save original values if needed
        if (!vehicle.gameObject.HasComponent<OriginalVehicleSettings>())
        {
            var settings = vehicle.gameObject.AddComponent<OriginalVehicleSettings>();
            settings.originalFuelPerSec = scrapHeli.fuelPerSec;
        }
        
        // Set fuel consumption to zero
        scrapHeli.fuelPerSec = 0f;
        
        // Add a small amount of fuel to keep it running
        var fuelSystem = vehicle.GetFuelSystem();
        if (fuelSystem != null)
        {
            fuelSystem.AddFuel(10);
        }
    }
    
    // For other vehicles, just add some fuel
    else
    {
        var fuelSystem = vehicle.GetFuelSystem();
        if (fuelSystem != null)
        {
            fuelSystem.AddFuel(10);
        }
    }
}

// Add this class to store original vehicle settings
private class OriginalVehicleSettings : MonoBehaviour
{
    public float originalFuelPerSec;
}



