using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Plugins;
using Oxide.Game.Rust.Cui;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Furnace Upgrades", "Frizzo420", "1.0.0")]
    [Description("Allows players to upgrade furnaces with various performance enhancements")]
    class FurnaceUpgrades : RustPlugin
    {
        #region Fields
        private Configuration config;
        private Dictionary<NetworkableId, FurnaceData> furnaceData = new Dictionary<NetworkableId, FurnaceData>();
        private const string PERMISSION_USE = "furnaceupgrades.use";
        
        [PluginReference] private Plugin Economics;
        [PluginReference] private Plugin ServerRewards;
        [PluginReference] private Plugin Notify;
        [PluginReference] private Plugin Toastify;
        #endregion

        #region Configuration
        private class Configuration
        {
            public GeneralSettings GeneralSettings { get; set; } = new GeneralSettings();
            public UISettings UISettings { get; set; } = new UISettings();
            public UpgradeSettings UpgradeSettings { get; set; } = new UpgradeSettings();
            public EconomySettings EconomySettings { get; set; } = new EconomySettings();
            public NotificationSettings NotificationSettings { get; set; } = new NotificationSettings();
            public FurnaceTypeSettings FurnaceTypeSettings { get; set; } = new FurnaceTypeSettings();
        }

        private class GeneralSettings
        {
            public bool KeepAttributesWhenRemoving { get; set; } = true;
            public bool RequireOwnership { get; set; } = true;
            public bool EnableAutoSplitFeature { get; set; } = true;
            public bool EnableAutoFuelFeature { get; set; } = true;
            public int MaxTotalUpgradeLevel { get; set; } = 20; // Maximum combined levels across all upgrades
        }

        private class UISettings
        {
            public bool DisableStatusPanel { get; set; } = false;
            public string MainColor { get; set; } = "0.1 0.1 0.1 0.9";
            public string ButtonColor { get; set; } = "0.3 0.5 0.3 0.8";
            public string ProgressBarColor { get; set; } = "0.2 0.8 0.2 1";
            public string TextColor { get; set; } = "1 1 1 1";
            public int FontSize { get; set; } = 14;
            
            // Status panel position
            public string StatusPanelAnchorMin { get; set; } = "0.54 0.43";
            public string StatusPanelAnchorMax { get; set; } = "0.94 0.51";
            public string StatusPanelOffsetMin { get; set; } = "0 0";
            public string StatusPanelOffsetMax { get; set; } = "0 0";
            
            // Upgrade button position
            public string UpgradeButtonAnchorMin { get; set; } = "0.7 0.03";
            public string UpgradeButtonAnchorMax { get; set; } = "0.9 0.11";
            public string UpgradeButtonOffsetMin { get; set; } = "0 0";
            public string UpgradeButtonOffsetMax { get; set; } = "0 0";
            
            // Upgrade menu position
            public string UpgradeMenuAnchorMin { get; set; } = "0.3 0.3";
            public string UpgradeMenuAnchorMax { get; set; } = "0.7 0.7";
            public string UpgradeMenuOffsetMin { get; set; } = "0 0";
            public string UpgradeMenuOffsetMax { get; set; } = "0 0";
            
            // UI text
            public string UpgradeButtonText { get; set; } = "Upgrade Furnace";
            public string UpgradeMenuTitle { get; set; } = "Furnace Upgrades";
            public string CloseButtonText { get; set; } = "X";
            public string UpgradeText { get; set; } = "Upgrade";
            public string MaxLevelText { get; set; } = "MAX";
            
            // UI positioning
            public bool EnableDraggableUI { get; set; } = true;
            public float DragStep { get; set; } = 0.01f; // Step size for movement
        }

        private class UpgradeSettings
        {
            public Dictionary<string, UpgradeOption> Options { get; set; } = new Dictionary<string, UpgradeOption>
            {
                ["smeltingspeed"] = new UpgradeOption { 
                    MaxLevel = 5, 
                    CostPerLevel = 100,
                    DisplayName = "Smelting Speed",
                    Description = "Increases the speed at which items are smelted",
                    Enabled = true
                },
                ["fuelspeed"] = new UpgradeOption { 
                    MaxLevel = 5, 
                    CostPerLevel = 100,
                    DisplayName = "Fuel Efficiency",
                    Description = "Reduces the rate at which fuel is consumed",
                    Enabled = true
                },
                ["resourceoutput"] = new UpgradeOption { 
                    MaxLevel = 5, 
                    CostPerLevel = 150,
                    DisplayName = "Resource Output",
                    Description = "Increases the amount of resources produced",
                    Enabled = true
                },
                ["charcoalmultiplier"] = new UpgradeOption { 
                    MaxLevel = 5, 
                    CostPerLevel = 100,
                    DisplayName = "Charcoal Multiplier",
                    Description = "Increases the amount of charcoal produced",
                    Enabled = true
                },
                ["autosplit"] = new UpgradeOption { 
                    MaxLevel = 1, 
                    CostPerLevel = 200,
                    DisplayName = "Auto Split",
                    Description = "Automatically splits resources across slots",
                    Enabled = true,
                    IsBooleanUpgrade = true
                },
                ["autofuel"] = new UpgradeOption { 
                    MaxLevel = 1, 
                    CostPerLevel = 200,
                    DisplayName = "Auto Fuel",
                    Description = "Automatically adds fuel when needed",
                    Enabled = true,
                    IsBooleanUpgrade = true
                }
            };
        }

        private class UpgradeOption
        {
            public int MaxLevel { get; set; } = 5;
            public int CostPerLevel { get; set; } = 100;
            public string DisplayName { get; set; } = "Upgrade";
            public string Description { get; set; } = "Improves furnace performance";
            public bool Enabled { get; set; } = true;
            public bool IsBooleanUpgrade { get; set; } = false;
            public float ValueMultiplierPerLevel { get; set; } = 0.2f; // 20% increase per level
        }

        private class EconomySettings
        {
            public string PreferredEconomyPlugin { get; set; } = "Economics"; // "Economics", "ServerRewards", or "Both"
            public bool AllowPayWithResources { get; set; } = false;
            public Dictionary<string, int> ResourceCosts { get; set; } = new Dictionary<string, int>
            {
                ["scrap"] = 50,
                ["metal.refined"] = 10,
                ["metal.fragments"] = 250
            };
        }

        private class NotificationSettings
        {
            public bool UseNotifyPlugin { get; set; } = true;
            public bool UseToastifyPlugin { get; set; } = true;
            public bool UsePopupNotifications { get; set; } = true;
            public bool UseChatNotifications { get; set; } = true;
            public string UpgradeSuccessMessage { get; set; } = "You've upgraded your furnace! New level: {0}/{1}";
            public string UpgradeFailedMessage { get; set; } = "Upgrade failed: {0}";
        }

        private class FurnaceTypeSettings
        {
            public Dictionary<string, bool> SupportedFurnaceTypes { get; set; } = new Dictionary<string, bool>
            {
                ["furnace"] = true,
                ["furnace.large"] = true,
                ["refinery_small_deployed"] = true,
                ["electric.furnace"] = true
            };
            
            public Dictionary<string, float> TypeMultipliers { get; set; } = new Dictionary<string, float>
            {
                ["furnace"] = 1.0f,
                ["furnace.large"] = 1.5f,
                ["refinery_small_deployed"] = 0.8f,
                ["electric.furnace"] = 1.2f
            };
        }
        #endregion

        #region Classes
        private class FurnaceData
        {
            public ulong OwnerId { get; set; }
            public float SmeltingSpeed { get; set; } = 1f;
            public float FuelSpeed { get; set; } = 1f;
            public float ResourceOutput { get; set; } = 1f;
            public float CharcoalMultiplier { get; set; } = 1f;
            public bool AutoSplitCookables { get; set; }
            public bool AutoAddFuel { get; set; }
        }
        #endregion

        #region Oxide Hooks
        private void Init()
        {
            permission.RegisterPermission(PERMISSION_USE, this);
            permission.RegisterPermission("furnaceupgrades.admin", this);
            
            // Load the configuration
            LoadConfig();
            
            // Load saved furnace data
            LoadData();
            
            // Register commands
            cmd.AddChatCommand("furnaceupgrade", this, "CmdFurnaceUpgrade");
            cmd.AddChatCommand("furnaceui", this, "CmdFurnaceUI");
            cmd.AddChatCommand("furnaceinfo", this, "CmdFurnaceInfo");
            
            // Register console commands
            cmd.AddConsoleCommand("furnaceupgrade.doupgrade", this, "ConsoleCmd_DoUpgrade");
        }

        private void OnServerInitialized()
        {
            // Initialize references to other plugins
            Economics = plugins.Find("Economics");
            ServerRewards = plugins.Find("ServerRewards");
            Notify = plugins.Find("Notify");
            Toastify = plugins.Find("Toastify");
            
            // Log plugin initialization
            Puts("Furnace Upgrades plugin initialized!");
            Puts($"Economics plugin found: {Economics != null}");
            Puts($"ServerRewards plugin found: {ServerRewards != null}");
            Puts($"Notify plugin found: {Notify != null}");
            Puts($"Toastify plugin found: {Toastify != null}");
        }

        private void Unload()
        {
            // Save furnace data
            SaveData();
            
            // Clean up UI for all players
            foreach (var player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, "FurnaceUpgradeButton");
                CuiHelper.DestroyUi(player, "FurnaceStatusPanel");
                CuiHelper.DestroyUi(player, "FurnaceUpgradeMenu");
            }
        }

        private object OnItemAddedToContainer(ItemContainer container, Item item)
        {
            if (container?.entityOwner == null) return null;
            
            var oven = container.entityOwner as BaseOven;
            if (oven == null || !furnaceData.ContainsKey(oven.net.ID)) return null;
            
            var data = furnaceData[oven.net.ID];
            
            // Auto split cookables
            if (data.AutoSplitCookables && item.info.shortname != "wood" && oven.inventory.itemList.Count > 1)
            {
                // Logic to split items across slots
                // ...
            }
            
            // Auto add fuel
            if (data.AutoAddFuel && item.info.shortname == "wood")
            {
                // Logic to automatically add fuel
                // ...
            }
            
            return null;
        }

        private void OnLootEntity(BasePlayer player, BaseEntity entity)
        {
            var oven = entity as BaseOven;
            if (oven != null && IsSupportedFurnace(oven))
            {
                if (!furnaceData.ContainsKey(oven.net.ID))
                {
                    InitializeFurnace(oven);
                }
                
                // Show UI
                if (!config.UISettings.DisableStatusPanel)
                {
                    ShowStatusUI(player, oven);
                }
                
                // Show upgrade button
                ShowUpgradeButton(player, oven);
            }
        }

        private void OnLootEntityEnd(BasePlayer player, BaseEntity entity)
        {
            var oven = entity as BaseOven;
            if (oven != null && IsSupportedFurnace(oven))
            {
                CuiHelper.DestroyUi(player, "FurnaceUpgradeButton");
                CuiHelper.DestroyUi(player, "FurnaceStatusPanel");
            }
        }

        private void OnEntityKill(BaseNetworkable entity)
        {
            var oven = entity as BaseOven;
            if (oven != null && furnaceData.ContainsKey(oven.net.ID))
            {
                if (config.GeneralSettings.KeepAttributesWhenRemoving)
                {
                    // Keep the data for when the furnace is placed again
                    // This would require tracking the item ID
                }
                else
                {
                    // Remove the furnace data
                    furnaceData.Remove(oven.net.ID);
                    SaveData();
                }
            }
        }
        #endregion

        #region Data Management
        private void LoadData()
        {
            try
            {
                var data = Interface.Oxide.DataFileSystem.ReadObject<Dictionary<string, FurnaceData>>(Name);
                furnaceData = new Dictionary<NetworkableId, FurnaceData>();
                
                if (data != null)
                {
                    foreach (var kvp in data)
                    {
                        ulong id;
                        if (ulong.TryParse(kvp.Key, out id))
                        {
                            furnaceData[new NetworkableId(id)] = kvp.Value;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                PrintError($"Error loading data: {ex.Message}");
                furnaceData = new Dictionary<NetworkableId, FurnaceData>();
            }
        }

        private void SaveData()
        {
            if (furnaceData != null)
            {
                var data = new Dictionary<string, FurnaceData>();
                foreach (var kvp in furnaceData)
                {
                    data[kvp.Key.Value.ToString()] = kvp.Value;
                }
                Interface.Oxide.DataFileSystem.WriteObject(Name, data);
            }
        }
        #endregion

        #region Helper Methods
        private bool IsSupportedFurnace(BaseOven oven)
        {
            if (oven == null) return false;
            
            // Debug output
            Puts($"Checking furnace: {oven.ShortPrefabName}");
            
            // Check if the entity is a furnace type that we support
            string prefabName = oven.PrefabName;
            string shortname = oven.ShortPrefabName;
            
            // Check against our configured supported furnace types
            foreach (var furnaceType in config.FurnaceTypeSettings.SupportedFurnaceTypes)
            {
                if (furnaceType.Value && (prefabName.Contains(furnaceType.Key) || shortname == furnaceType.Key))
                {
                    Puts($"Furnace type {shortname} is supported");
                    return true;
                }
            }
            
            Puts($"Furnace type {shortname} is NOT supported");
            return false;
        }

        private void InitializeFurnace(BaseOven oven)
        {
            if (oven == null) return;
            
            var data = new FurnaceData
            {
                OwnerId = oven.OwnerID,
                SmeltingSpeed = 1f,
                FuelSpeed = 1f,
                ResourceOutput = 1f,
                CharcoalMultiplier = 1f,
                AutoSplitCookables = false,
                AutoAddFuel = false
            };
            
            furnaceData[oven.net.ID] = data;
            SaveData();
        }

        private void NotifyPlayer(BasePlayer player, string message)
        {
            if (player == null) return;
            
            if (config.NotificationSettings.UseChatNotifications)
            {
                SendReply(player, message);
            }
            
            if (config.NotificationSettings.UsePopupNotifications)
            {
                player.ChatMessage(message);
            }
            
            if (config.NotificationSettings.UseNotifyPlugin && Notify != null)
            {
                Notify.Call("SendNotify", player, message);
            }
            
            if (config.NotificationSettings.UseToastifyPlugin && Toastify != null)
            {
                Toastify.Call("SendToast", player, message);
            }
        }
        #endregion

        #region UI Methods
        private void ShowStatusUI(BasePlayer player, BaseOven oven)
        {
            if (player == null || oven == null) return;
            
            var data = furnaceData[oven.net.ID];
            
            var container = new CuiElementContainer();
            
            // Calculate new size (50% smaller)
            string[] anchorMinParts = config.UISettings.StatusPanelAnchorMin.Split(' ');
            string[] anchorMaxParts = config.UISettings.StatusPanelAnchorMax.Split(' ');
            
            container.Add(new CuiPanel
            {
                RectTransform = { 
                    AnchorMin = config.UISettings.StatusPanelAnchorMin, 
                    AnchorMax = config.UISettings.StatusPanelAnchorMax,
                    OffsetMin = config.UISettings.StatusPanelOffsetMin,
                    OffsetMax = config.UISettings.StatusPanelOffsetMax
                },
                Image = { Color = config.UISettings.MainColor }
            }, "Hud", "FurnaceStatusPanel");
            
            // Adjust font size for smaller panel
            int adjustedFontSize = (int)(config.UISettings.FontSize * 0.8);
            
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                Text = { 
                    Text = $"Smelting: {data.SmeltingSpeed}x | Fuel: {data.FuelSpeed}x | Output: {data.ResourceOutput}x | Charcoal: {data.CharcoalMultiplier}x", 
                    FontSize = adjustedFontSize, 
                    Align = TextAnchor.MiddleCenter, 
                    Color = config.UISettings.TextColor 
                }
            }, "FurnaceStatusPanel");
            
            CuiHelper.AddUi(player, container);
        }

        private void ShowUpgradeButton(BasePlayer player, BaseOven oven)
        {
            if (player == null || oven == null) return;
            
            // Check permission
            if (config.GeneralSettings.RequireOwnership && oven.OwnerID != 0 && oven.OwnerID != player.userID)
            {
                return;
            }
            
            if (!permission.UserHasPermission(player.UserIDString, PERMISSION_USE))
            {
                return;
            }
            
            var container = new CuiElementContainer();
            
            container.Add(new CuiPanel
            {
                RectTransform = { 
                    AnchorMin = config.UISettings.UpgradeButtonAnchorMin, 
                    AnchorMax = config.UISettings.UpgradeButtonAnchorMax,
                    OffsetMin = config.UISettings.UpgradeButtonOffsetMin,
                    OffsetMax = config.UISettings.UpgradeButtonOffsetMax
                },
                Image = { Color = config.UISettings.ButtonColor }
            }, "Hud", "FurnaceUpgradeButton");
            
            container.Add(new CuiButton
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                Button = { 
                    Color = config.UISettings.ButtonColor, 
                    Command = $"furnaceupgrade.menu {oven.net.ID.Value}" 
                },
                Text = { 
                    Text = config.UISettings.UpgradeButtonText, 
                    FontSize = config.UISettings.FontSize, 
                    Align = TextAnchor.MiddleCenter, 
                    Color = config.UISettings.TextColor 
                }
            }, "FurnaceUpgradeButton");
            
            CuiHelper.AddUi(player, container);
        }

        private void ShowUpgradeMenu(BasePlayer player, BaseOven oven)
        {
            if (player == null || oven == null) return;
            
            var data = furnaceData[oven.net.ID];
            
            var container = new CuiElementContainer();
            
            // Main panel
            container.Add(new CuiPanel
            {
                RectTransform = { 
                    AnchorMin = config.UISettings.UpgradeMenuAnchorMin, 
                    AnchorMax = config.UISettings.UpgradeMenuAnchorMax,
                    OffsetMin = config.UISettings.UpgradeMenuOffsetMin,
                    OffsetMax = config.UISettings.UpgradeMenuOffsetMax
                },
                Image = { Color = config.UISettings.MainColor }
            }, "Overlay", "FurnaceUpgradeMenu");
            
            // Title
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = "0 0.9", AnchorMax = "1 1" },
                Text = { 
                    Text = config.UISettings.UpgradeMenuTitle, 
                    FontSize = config.UISettings.FontSize + 6, 
                    Align = TextAnchor.MiddleCenter, 
                    Color = config.UISettings.TextColor 
                }
            }, "FurnaceUpgradeMenu");
            
            // Close button
            container.Add(new CuiButton
            {
                RectTransform = { AnchorMin = "0.9 0.9", AnchorMax = "1 1" },
                Button = { 
                    Color = "0.7 0.2 0.2 0.8", 
                    Command = "furnaceupgrade.close" 
                },
                Text = { 
                    Text = config.UISettings.CloseButtonText, 
                    FontSize = config.UISettings.FontSize + 6, 
                    Align = TextAnchor.MiddleCenter, 
                    Color = config.UISettings.TextColor 
                }
            }, "FurnaceUpgradeMenu");
            
            // Upgrade options
            float yPos = 0.8f;
            float height = 0.1f;
            
            // Add each upgrade option
            foreach (var upgrade in config.UpgradeSettings.Options)
            {
                if (!upgrade.Value.Enabled) continue;
                
                float currentValue = 1f;
                bool isBooleanUpgrade = upgrade.Value.IsBooleanUpgrade;
                
                switch (upgrade.Key)
                {
                    case "smeltingspeed":
                        currentValue = data.SmeltingSpeed;
                        break;
                    case "fuelspeed":
                        currentValue = data.FuelSpeed;
                        break;
                    case "resourceoutput":
                        currentValue = data.ResourceOutput;
                        break;
                    case "charcoalmultiplier":
                        currentValue = data.CharcoalMultiplier;
                        break;
                    case "autosplit":
                        currentValue = data.AutoSplitCookables ? 2f : 1f;
                        break;
                    case "autofuel":
                        currentValue = data.AutoAddFuel ? 2f : 1f;
                        break;
                }
                
                AddUpgradeOption(container, upgrade.Value.DisplayName, currentValue, upgrade.Key, 
                    upgrade.Value.MaxLevel, yPos, height, oven.net.ID, isBooleanUpgrade, upgrade.Value.Description);
                
                yPos -= height + 0.02f;
            }
            
            CuiHelper.AddUi(player, container);
        }

        private void AddUpgradeOption(CuiElementContainer container, string name, float currentValue, string upgradeType, 
            int maxLevel, float yPos, float height, NetworkableId furnaceId, bool isBooleanUpgrade = false, string description = "")
        {
            int currentLevel;
            
            if (isBooleanUpgrade)
            {
                currentLevel = currentValue > 1f ? 1 : 0;
            }
            else
            {
                currentLevel = (int)Math.Round((currentValue - 1) * 5);
            }
            
            int cost = config.UpgradeSettings.Options[upgradeType].CostPerLevel * (currentLevel + 1);
            
            // Option panel
            container.Add(new CuiPanel
            {
                RectTransform = { AnchorMin = $"0.05 {yPos - height}", AnchorMax = $"0.95 {yPos}" },
                Image = { Color = "0.2 0.2 0.2 0.8" }
            }, "FurnaceUpgradeMenu", $"Option_{upgradeType}");
            
            // Option name and value
            string displayValue = isBooleanUpgrade ? (currentLevel > 0 ? "Enabled" : "Disabled") : $"{currentValue}x";
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = "0.05 0.5", AnchorMax = "0.5 1" },
                Text = { 
                    Text = $"{name}: {displayValue}", 
                    FontSize = config.UISettings.FontSize, 
                    Align = TextAnchor.MiddleLeft, 
                    Color = config.UISettings.TextColor 
                }
            }, $"Option_{upgradeType}");
            
            // Description
            if (!string.IsNullOrEmpty(description))
            {
                container.Add(new CuiLabel
                {
                    RectTransform = { AnchorMin = "0.05 0", AnchorMax = "0.5 0.5" },
                    Text = { 
                        Text = description, 
                        FontSize = config.UISettings.FontSize - 2, 
                        Align = TextAnchor.MiddleLeft, 
                        Color = "0.8 0.8 0.8 1" 
                    }
                }, $"Option_{upgradeType}");
            }
            
            // Progress bar background
            container.Add(new CuiPanel
            {
                RectTransform = { AnchorMin = "0.5 0.3", AnchorMax = "0.8 0.7" },
                Image = { Color = "0.1 0.1 0.1 1" }
            }, $"Option_{upgradeType}", $"Progress_{upgradeType}");
            
            // Progress bar fill
            if (currentLevel > 0)
            {
                float fillAmount = isBooleanUpgrade ? 1f : (float)currentLevel / maxLevel;
                container.Add(new CuiPanel
                {
                    RectTransform = { AnchorMin = "0 0", AnchorMax = $"{fillAmount} 1" },
                    Image = { Color = config.UISettings.ProgressBarColor }
                }, $"Progress_{upgradeType}");
            }
            
            // Upgrade button (if not maxed)
            if (currentLevel < maxLevel)
            {
                // Use our new command format
                string command = $"furnaceupgrade.doupgrade {furnaceId.Value} {upgradeType}";
                Puts($"Creating upgrade button with command: {command}");
                
                container.Add(new CuiButton
                {
                    RectTransform = { AnchorMin = "0.82 0.2", AnchorMax = "0.98 0.8" },
                    Button = { 
                        Color = config.UISettings.ButtonColor, 
                        Command = command
                    },
                    Text = { 
                        Text = $"{config.UISettings.UpgradeText}\n{cost}", 
                        FontSize = config.UISettings.FontSize - 2, 
                        Align = TextAnchor.MiddleCenter, 
                        Color = config.UISettings.TextColor 
                    }
                }, $"Option_{upgradeType}");
            }
            else
            {
                // Max level indicator
                container.Add(new CuiLabel
                {
                    RectTransform = { AnchorMin = "0.82 0.2", AnchorMax = "0.98 0.8" },
                    Text = { 
                        Text = config.UISettings.MaxLevelText, 
                        FontSize = config.UISettings.FontSize, 
                        Align = TextAnchor.MiddleCenter, 
                        Color = "0.8 0.8 0.8 1" 
                    }
                }, $"Option_{upgradeType}");
            }
        }
        #endregion

        #region Commands
        private void CmdFurnaceUpgrade(BasePlayer player, string command, string[] args)
        {
            if (player == null) return;
            if (!permission.UserHasPermission(player.userID.ToString(), PERMISSION_USE))
            {
                SendReply(player, "You don't have permission to use furnace upgrades.");
                return;
            }
            
            // Debug output to help diagnose issues
            Puts($"FurnaceUpgrade command received from {player.displayName}. Args count: {args.Length}");
            if (args.Length > 0)
                Puts($"First arg: {args[0]}");
            
            // Handle "close" command
            if (args.Length > 0 && args[0] == "close")
            {
                CuiHelper.DestroyUi(player, "FurnaceUpgradeMenu");
                return;
            }
            
            // Handle upgrade command
            if (args.Length > 2 && args[0] == "upgrade")
            {
                Puts($"Processing upgrade command. Args: {string.Join(", ", args)}");
                
                // Parse furnace ID
                ulong furnaceIdNum;
                if (!ulong.TryParse(args[1], out furnaceIdNum))
                {
                    SendReply(player, $"Invalid furnace ID: {args[1]}");
                    Puts($"Failed to parse furnace ID: {args[1]}");
                    return;
                }
                
                Puts($"Parsed furnace ID: {furnaceIdNum}");
                NetworkableId furnaceId = new NetworkableId(furnaceIdNum);
                
                // Find the furnace entity
                BaseNetworkable entity = BaseNetworkable.serverEntities.Find(furnaceId);
                Puts($"Found entity: {(entity != null ? "Yes" : "No")}");
                
                BaseOven oven = entity as BaseOven;
                Puts($"Entity is BaseOven: {(oven != null ? "Yes" : "No")}");
                
                if (oven == null || !IsSupportedFurnace(oven))
                {
                    SendReply(player, "Furnace not found or not supported.");
                    Puts($"Furnace not found or not supported. ID: {furnaceIdNum}");
                    return;
                }
                
                // Check if player has permission to upgrade this furnace
                if (config.GeneralSettings.RequireOwnership && oven.OwnerID != 0 && oven.OwnerID != player.userID)
                {
                    SendReply(player, "You can only upgrade furnaces you own.");
                    return;
                }
                
                // Get upgrade type
                string upgradeType = args[2];
                if (!config.UpgradeSettings.Options.ContainsKey(upgradeType))
                {
                    SendReply(player, $"Invalid upgrade type: {upgradeType}");
                    return;
                }
                
                // Get furnace data
                if (!furnaceData.ContainsKey(furnaceId))
                {
                    InitializeFurnace(oven);
                }
                
                var data = furnaceData[furnaceId];
                
                // Calculate current level and cost
                float currentValue = 1f;
                bool isBooleanUpgrade = config.UpgradeSettings.Options[upgradeType].IsBooleanUpgrade;
                
                switch (upgradeType)
                {
                    case "smeltingspeed":
                        currentValue = data.SmeltingSpeed;
                        break;
                    case "fuelspeed":
                        currentValue = data.FuelSpeed;
                        break;
                    case "resourceoutput":
                        currentValue = data.ResourceOutput;
                        break;
                    case "charcoalmultiplier":
                        currentValue = data.CharcoalMultiplier;
                        break;
                    case "autosplit":
                        currentValue = data.AutoSplitCookables ? 2f : 1f;
                        break;
                    case "autofuel":
                        currentValue = data.AutoAddFuel ? 2f : 1f;
                        break;
                }
                
                int currentLevel;
                if (isBooleanUpgrade)
                {
                    currentLevel = currentValue > 1f ? 1 : 0;
                }
                else
                {
                    currentLevel = (int)Math.Round((currentValue - 1) * 5);
                }
                
                int maxLevel = config.UpgradeSettings.Options[upgradeType].MaxLevel;
                
                // Check if already at max level
                if (currentLevel >= maxLevel)
                {
                    SendReply(player, "This upgrade is already at maximum level.");
                    return;
                }
                
                // Calculate cost
                int cost = config.UpgradeSettings.Options[upgradeType].CostPerLevel * (currentLevel + 1);
                
                // Check if player can afford the upgrade
                bool canAfford = false;
                
                // Try Economics first
                if (Economics != null && (config.EconomySettings.PreferredEconomyPlugin == "Economics" || 
                                         config.EconomySettings.PreferredEconomyPlugin == "Both"))
                {
                    double balance = (double)Economics.Call("Balance", player.userID);
                    if (balance >= cost)
                    {
                        Economics.Call("Withdraw", player.userID, (double)cost);
                        canAfford = true;
                        Puts($"Player {player.displayName} paid {cost} via Economics");
                    }
                }
                // Try ServerRewards if Economics failed or is not preferred
                else if (ServerRewards != null && (config.EconomySettings.PreferredEconomyPlugin == "ServerRewards" || 
                                                  config.EconomySettings.PreferredEconomyPlugin == "Both"))
                {
                    int points = (int)ServerRewards.Call("CheckPoints", player.userID);
                    if (points >= cost)
                    {
                        ServerRewards.Call("TakePoints", player.userID, cost);
                        canAfford = true;
                        Puts($"Player {player.displayName} paid {cost} via ServerRewards");
                    }
                }
                
                if (!canAfford)
                {
                    SendReply(player, $"You cannot afford this upgrade. Cost: {cost} points/coins.");
                    return;
                }
                
                // Apply the upgrade
                switch (upgradeType)
                {
                    case "smeltingspeed":
                        data.SmeltingSpeed = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                        break;
                    case "fuelspeed":
                        data.FuelSpeed = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                        break;
                    case "resourceoutput":
                        data.ResourceOutput = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                        break;
                    case "charcoalmultiplier":
                        data.CharcoalMultiplier = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                        break;
                    case "autosplit":
                        data.AutoSplitCookables = true;
                        break;
                    case "autofuel":
                        data.AutoAddFuel = true;
                        break;
                }
                
                // Save data
                SaveData();
                
                // Notify player
                string message = string.Format(config.NotificationSettings.UpgradeSuccessMessage, currentLevel + 1, maxLevel);
                NotifyPlayer(player, message);
                
                // Update UI
                CuiHelper.DestroyUi(player, "FurnaceUpgradeMenu");
                ShowUpgradeMenu(player, oven);
                
                return;
            }
            
            // If no arguments or not a recognized command, show the upgrade menu
            if (args.Length > 0)
            {
                Puts($"Attempting to show upgrade menu for furnace ID: {args[0]}");
                ulong furnaceIdNum;
                if (ulong.TryParse(args[0], out furnaceIdNum))
                {
                    NetworkableId furnaceId = new NetworkableId(furnaceIdNum);
                    BaseNetworkable entity = BaseNetworkable.serverEntities.Find(furnaceId);
                    Puts($"Found entity for menu: {(entity != null ? "Yes" : "No")}");
                    
                    BaseOven oven = entity as BaseOven;
                    Puts($"Entity is BaseOven for menu: {(oven != null ? "Yes" : "No")}");
                    
                    if (oven != null && IsSupportedFurnace(oven))
                    {
                        ShowUpgradeMenu(player, oven);
                        return;
                    }
                    else
                    {
                        SendReply(player, $"Furnace not found or not supported. ID: {furnaceIdNum}");
                        Puts($"Furnace not found or not supported for menu. ID: {furnaceIdNum}");
                    }
                }
                else
                {
                    SendReply(player, $"Invalid furnace ID format: {args[0]}");
                    Puts($"Invalid furnace ID format for menu: {args[0]}");
                }
            }
            
            SendReply(player, "Usage: /furnaceupgrade <furnaceId>");
        }
        #endregion

        protected override void LoadDefaultConfig()
        {
            Config.Clear();
            Config["GeneralSettings"] = new GeneralSettings();
            Config["UISettings"] = new UISettings();
            Config["UpgradeSettings"] = new UpgradeSettings();
            Config["EconomySettings"] = new EconomySettings();
            Config["NotificationSettings"] = new NotificationSettings();
            Config["FurnaceTypeSettings"] = new FurnaceTypeSettings();
            SaveConfig();
            PrintWarning("Default configuration has been created.");
        }

        private void LoadConfig()
        {
            config = new Configuration();
            
            try
            {
                // Load GeneralSettings
                if (Config["GeneralSettings"] is Dictionary<string, object> generalData)
                {
                    config.GeneralSettings.KeepAttributesWhenRemoving = GetConfigValue(generalData, "KeepAttributesWhenRemoving", true);
                    config.GeneralSettings.RequireOwnership = GetConfigValue(generalData, "RequireOwnership", true);
                    config.GeneralSettings.EnableAutoSplitFeature = GetConfigValue(generalData, "EnableAutoSplitFeature", true);
                    config.GeneralSettings.EnableAutoFuelFeature = GetConfigValue(generalData, "EnableAutoFuelFeature", true);
                    config.GeneralSettings.MaxTotalUpgradeLevel = GetConfigValue(generalData, "MaxTotalUpgradeLevel", 20);
                }
                
                // Load UISettings
                if (Config["UISettings"] is Dictionary<string, object> uiData)
                {
                    config.UISettings.DisableStatusPanel = GetConfigValue(uiData, "DisableStatusPanel", false);
                    config.UISettings.MainColor = GetConfigValue(uiData, "MainColor", "0.1 0.1 0.1 0.9");
                    config.UISettings.ButtonColor = GetConfigValue(uiData, "ButtonColor", "0.3 0.5 0.3 0.8");
                    config.UISettings.ProgressBarColor = GetConfigValue(uiData, "ProgressBarColor", "0.2 0.8 0.2 1");
                    config.UISettings.TextColor = GetConfigValue(uiData, "TextColor", "1 1 1 1");
                    config.UISettings.FontSize = GetConfigValue(uiData, "FontSize", 14);
                    
                    // Status panel position - UPDATED DEFAULT VALUES
                    config.UISettings.StatusPanelAnchorMin = GetConfigValue(uiData, "StatusPanelAnchorMin", "0.54 0.43");
                    config.UISettings.StatusPanelAnchorMax = GetConfigValue(uiData, "StatusPanelAnchorMax", "0.94 0.51");
                    config.UISettings.StatusPanelOffsetMin = GetConfigValue(uiData, "StatusPanelOffsetMin", "0 0");
                    config.UISettings.StatusPanelOffsetMax = GetConfigValue(uiData, "StatusPanelOffsetMax", "0 0");
                    
                    // Upgrade button position - UPDATED DEFAULT VALUES
                    config.UISettings.UpgradeButtonAnchorMin = GetConfigValue(uiData, "UpgradeButtonAnchorMin", "0.7 0.03");
                    config.UISettings.UpgradeButtonAnchorMax = GetConfigValue(uiData, "UpgradeButtonAnchorMax", "0.9 0.11");
                    config.UISettings.UpgradeButtonOffsetMin = GetConfigValue(uiData, "UpgradeButtonOffsetMin", "0 0");
                    config.UISettings.UpgradeButtonOffsetMax = GetConfigValue(uiData, "UpgradeButtonOffsetMax", "0 0");
                    
                    // Upgrade menu position
                    config.UISettings.UpgradeMenuAnchorMin = GetConfigValue(uiData, "UpgradeMenuAnchorMin", "0.3 0.3");
                    config.UISettings.UpgradeMenuAnchorMax = GetConfigValue(uiData, "UpgradeMenuAnchorMax", "0.7 0.7");
                    config.UISettings.UpgradeMenuOffsetMin = GetConfigValue(uiData, "UpgradeMenuOffsetMin", "0 0");
                    config.UISettings.UpgradeMenuOffsetMax = GetConfigValue(uiData, "UpgradeMenuOffsetMax", "0 0");
                    
                    // UI text
                    config.UISettings.UpgradeButtonText = GetConfigValue(uiData, "UpgradeButtonText", "Upgrade Furnace");
                    config.UISettings.UpgradeMenuTitle = GetConfigValue(uiData, "UpgradeMenuTitle", "Furnace Upgrades");
                    config.UISettings.CloseButtonText = GetConfigValue(uiData, "CloseButtonText", "X");
                    config.UISettings.UpgradeText = GetConfigValue(uiData, "UpgradeText", "Upgrade");
                    config.UISettings.MaxLevelText = GetConfigValue(uiData, "MaxLevelText", "MAX");
                    
                    // UI positioning
                    config.UISettings.EnableDraggableUI = GetConfigValue(uiData, "EnableDraggableUI", true);
                    config.UISettings.DragStep = GetConfigValue(uiData, "DragStep", 0.01f);
                }
                
                // Load other settings...
            }
            catch (Exception ex)
            {
                PrintError($"Error loading configuration: {ex.Message}");
                LoadDefaultConfig();
            }
        }

        private void SaveConfig()
        {
            try
            {
                // Clear existing config to avoid merge issues
                Config.Clear();
                
                // Write the entire config object
                Config.WriteObject(config, true);
                
                // Debug output to verify config saving
                Puts($"Saved UI positions - Button: {config.UISettings.UpgradeButtonAnchorMin}, Status: {config.UISettings.StatusPanelAnchorMin}");
            }
            catch (Exception ex)
            {
                PrintError($"Error saving configuration: {ex.Message}");
            }
        }

        private T GetConfigValue<T>(Dictionary<string, object> data, string key, T defaultValue)
        {
            if (data.ContainsKey(key))
            {
                try
                {
                    return (T)Convert.ChangeType(data[key], typeof(T));
                }
                catch
                {
                    return defaultValue;
                }
            }
            return defaultValue;
        }

        private void NotifyPlayer(BasePlayer player, string message, string type = "info")
        {
            if (player == null) return;
            
            // Chat notification
            if (config.NotificationSettings.UseChatNotifications)
            {
                SendReply(player, message);
            }
            
            // Popup notification
            if (config.NotificationSettings.UsePopupNotifications)
            {
                player.ChatMessage(message);
            }
            
            // Notify plugin
            if (config.NotificationSettings.UseNotifyPlugin && Notify != null)
            {
                Notify.Call("SendNotify", player, message);
            }
            
            // Toastify plugin
            if (config.NotificationSettings.UseToastifyPlugin && Toastify != null)
            {
                Toastify.Call("SendToast", player, type, message);
            }
        }

        [ConsoleCommand("furnaceupgrade.debug")]
        private void ConsoleCmd_Debug(ConsoleSystem.Arg arg)
        {
            if (arg.Player() != null && !arg.Player().IsAdmin) return;
            
            Puts("=== FurnaceUpgrades Debug Info ===");
            Puts($"Registered furnaces: {furnaceData.Count}");
            
            foreach (var entry in furnaceData)
            {
                BaseNetworkable entity = BaseNetworkable.serverEntities.Find(entry.Key);
                string entityInfo = entity != null ? $"{entity.ShortPrefabName} (Owner: {entry.Value.OwnerId})" : "Not found";
                
                Puts($"Furnace ID: {entry.Key.Value}, Entity: {entityInfo}");
                Puts($"  SmeltingSpeed: {entry.Value.SmeltingSpeed}");
                Puts($"  FuelSpeed: {entry.Value.FuelSpeed}");
                Puts($"  ResourceOutput: {entry.Value.ResourceOutput}");
                Puts($"  CharcoalMultiplier: {entry.Value.CharcoalMultiplier}");
                Puts($"  AutoSplitCookables: {entry.Value.AutoSplitCookables}");
                Puts($"  AutoAddFuel: {entry.Value.AutoAddFuel}");
            }
            
            Puts("=== End Debug Info ===");
        }

        [ConsoleCommand("furnaceupgrade.menu")]
        private void ConsoleCmd_OpenMenu(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;
            
            if (arg.Args == null || arg.Args.Length < 1)
            {
                SendReply(player, "Usage: furnaceupgrade.menu <furnaceId>");
                return;
            }
            
            ulong furnaceIdNum;
            if (!ulong.TryParse(arg.Args[0], out furnaceIdNum))
            {
                SendReply(player, $"Invalid furnace ID: {arg.Args[0]}");
                return;
            }
            
            NetworkableId furnaceId = new NetworkableId(furnaceIdNum);
            BaseOven oven = BaseNetworkable.serverEntities.Find(furnaceId) as BaseOven;
            
            if (oven != null && IsSupportedFurnace(oven))
            {
                ShowUpgradeMenu(player, oven);
            }
            else
            {
                SendReply(player, $"Furnace not found or not supported. ID: {furnaceIdNum}");
            }
        }

        [ConsoleCommand("furnaceupgrade.direct")]
        private void ConsoleCmd_DirectUpgrade(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null || !player.IsAdmin) return;
            
            if (arg.Args == null || arg.Args.Length < 2)
            {
                SendReply(player, "Usage: furnaceupgrade.direct <furnaceId> <upgradeType>");
                return;
            }
            
            ulong furnaceIdNum;
            if (!ulong.TryParse(arg.Args[0], out furnaceIdNum))
            {
                SendReply(player, $"Invalid furnace ID: {arg.Args[0]}");
                return;
            }
            
            NetworkableId furnaceId = new NetworkableId(furnaceIdNum);
            BaseOven oven = BaseNetworkable.serverEntities.Find(furnaceId) as BaseOven;
            
            if (oven == null || !IsSupportedFurnace(oven))
            {
                SendReply(player, $"Furnace not found or not supported. ID: {furnaceIdNum}");
                return;
            }
            
            string upgradeType = arg.Args[1];
            if (!config.UpgradeSettings.Options.ContainsKey(upgradeType))
            {
                SendReply(player, $"Invalid upgrade type: {upgradeType}");
                return;
            }
            
            // Get furnace data
            if (!furnaceData.ContainsKey(furnaceId))
            {
                InitializeFurnace(oven);
            }
            
            var data = furnaceData[furnaceId];
            
            // Apply the upgrade directly (for testing)
            switch (upgradeType)
            {
                case "smeltingspeed":
                    data.SmeltingSpeed += 0.2f;
                    break;
                case "fuelspeed":
                    data.FuelSpeed += 0.2f;
                    break;
                case "resourceoutput":
                    data.ResourceOutput += 0.2f;
                    break;
                case "charcoalmultiplier":
                    data.CharcoalMultiplier += 0.2f;
                    break;
                case "autosplit":
                    data.AutoSplitCookables = true;
                    break;
                case "autofuel":
                    data.AutoAddFuel = true;
                    break;
            }
            
            // Save data
            SaveData();
            
            SendReply(player, $"Directly upgraded furnace {furnaceIdNum}, type: {upgradeType}");
            
            // Update UI if player has it open
            CuiHelper.DestroyUi(player, "FurnaceUpgradeMenu");
            ShowUpgradeMenu(player, oven);
        }

        [ChatCommand("furnaceui")]
        private void CmdFurnaceUI(BasePlayer player, string command, string[] args)
        {
            if (player == null || !player.IsAdmin) return;
            
            if (args.Length == 0)
            {
                SendReply(player, "Furnace UI Commands:");
                SendReply(player, "/furnaceui position - Shows current UI positions");
                SendReply(player, "/furnaceui move <button|status> <left|right|up|down> [steps=1] - Move UI element");
                SendReply(player, "/furnaceui reset - Reset UI positions to default");
                SendReply(player, "/furnaceui save - Save current UI positions");
                SendReply(player, "/furnaceui preview - Preview UI elements");
                return;
            }
            
            switch (args[0].ToLower())
            {
                case "position":
                    ShowUIPositions(player);
                    break;
                    
                case "move":
                    if (args.Length < 3)
                    {
                        SendReply(player, "Usage: /furnaceui move <button|status> <left|right|up|down> [steps=1]");
                        return;
                    }
                    
                    string element = args[1].ToLower();
                    string direction = args[2].ToLower();
                    int steps = args.Length > 3 ? int.Parse(args[3]) : 1;
                    MoveUIElement(player, element, direction, steps);
                    break;
                    
                case "reset":
                    ResetUIPositions(player);
                    break;
                    
                case "save":
                    SaveConfig();
                    SendReply(player, "UI positions saved to config.");
                    break;
                    
                case "preview":
                    ShowUIPreview(player);
                    break;
                    
                default:
                    SendReply(player, "Unknown command. Use /furnaceui for help.");
                    break;
            }
        }

        private void ShowUIPositions(BasePlayer player)
        {
            SendReply(player, "Current UI Positions:");
            SendReply(player, $"Status Panel: Min={config.UISettings.StatusPanelAnchorMin}, Max={config.UISettings.StatusPanelAnchorMax}");
            SendReply(player, $"Upgrade Button: Min={config.UISettings.UpgradeButtonAnchorMin}, Max={config.UISettings.UpgradeButtonAnchorMax}");
        }

        private void MoveUIElement(BasePlayer player, string element, string direction, int steps)
        {
            float stepSize = config.UISettings.DragStep * steps;
            
            if (element == "button")
            {
                string[] minParts = config.UISettings.UpgradeButtonAnchorMin.Split(' ');
                string[] maxParts = config.UISettings.UpgradeButtonAnchorMax.Split(' ');
                
                if (minParts.Length != 2 || maxParts.Length != 2)
                {
                    SendReply(player, "Invalid button position format in config.");
                    return;
                }
                
                float minX = float.Parse(minParts[0]);
                float minY = float.Parse(minParts[1]);
                float maxX = float.Parse(maxParts[0]);
                float maxY = float.Parse(maxParts[1]);
                
                // Adjust position based on direction
                switch (direction)
                {
                    case "left":
                        minX -= stepSize;
                        maxX -= stepSize;
                        break;
                    case "right":
                        minX += stepSize;
                        maxX += stepSize;
                        break;
                    case "up":
                        minY += stepSize;
                        maxY += stepSize;
                        break;
                    case "down":
                        minY -= stepSize;
                        maxY -= stepSize;
                        break;
                }
                
                // Ensure values stay within bounds (0-1)
                minX = Mathf.Clamp01(minX);
                minY = Mathf.Clamp01(minY);
                maxX = Mathf.Clamp01(maxX);
                maxY = Mathf.Clamp01(maxY);
                
                // Store old values for debugging
                string oldMin = config.UISettings.UpgradeButtonAnchorMin;
                string oldMax = config.UISettings.UpgradeButtonAnchorMax;
                
                // Update the config
                config.UISettings.UpgradeButtonAnchorMin = $"{minX} {minY}";
                config.UISettings.UpgradeButtonAnchorMax = $"{maxX} {maxY}";
                
                // Save the config to file
                SaveConfig();
                
                // Debug output
                SendReply(player, $"Moved upgrade button {direction}.");
                SendReply(player, $"Old position: Min={oldMin}, Max={oldMax}");
                SendReply(player, $"New position: Min={config.UISettings.UpgradeButtonAnchorMin}, Max={config.UISettings.UpgradeButtonAnchorMax}");
            }
            else if (element == "status")
            {
                string[] minParts = config.UISettings.StatusPanelAnchorMin.Split(' ');
                string[] maxParts = config.UISettings.StatusPanelAnchorMax.Split(' ');
                
                if (minParts.Length != 2 || maxParts.Length != 2)
                {
                    SendReply(player, "Invalid status panel position format in config.");
                    return;
                }
                
                float minX = float.Parse(minParts[0]);
                float minY = float.Parse(minParts[1]);
                float maxX = float.Parse(maxParts[0]);
                float maxY = float.Parse(maxParts[1]);
                
                // Adjust position based on direction
                switch (direction)
                {
                    case "left":
                        minX -= stepSize;
                        maxX -= stepSize;
                        break;
                    case "right":
                        minX += stepSize;
                        maxX += stepSize;
                        break;
                    case "up":
                        minY += stepSize;
                        maxY += stepSize;
                        break;
                    case "down":
                        minY -= stepSize;
                        maxY -= stepSize;
                        break;
                }
                
                // Ensure values stay within bounds (0-1)
                minX = Mathf.Clamp01(minX);
                minY = Mathf.Clamp01(minY);
                maxX = Mathf.Clamp01(maxX);
                maxY = Mathf.Clamp01(maxY);
                
                // Store old values for debugging
                string oldMin = config.UISettings.StatusPanelAnchorMin;
                string oldMax = config.UISettings.StatusPanelAnchorMax;
                
                // Update the config
                config.UISettings.StatusPanelAnchorMin = $"{minX} {minY}";
                config.UISettings.StatusPanelAnchorMax = $"{maxX} {maxY}";
                
                // Save the config to file
                SaveConfig();
                
                // Debug output
                SendReply(player, $"Moved status panel {direction}.");
                SendReply(player, $"Old position: Min={oldMin}, Max={oldMax}");
                SendReply(player, $"New position: Min={config.UISettings.StatusPanelAnchorMin}, Max={config.UISettings.StatusPanelAnchorMax}");
            }
            else
            {
                SendReply(player, "Invalid element. Use 'button' or 'status'.");
                return;
            }
            
            // Show preview of new positions
            ShowUIPreview(player);
        }

        private void ResetUIPositions(BasePlayer player)
        {
            // Reset to new default values
            config.UISettings.StatusPanelAnchorMin = "0.54 0.43";
            config.UISettings.StatusPanelAnchorMax = "0.94 0.51";
            config.UISettings.UpgradeButtonAnchorMin = "0.7 0.03";
            config.UISettings.UpgradeButtonAnchorMax = "0.9 0.11";
            
            SaveConfig();
            
            SendReply(player, "UI positions reset to default values.");
            ShowUIPreview(player);
        }

        private void ShowUIPreview(BasePlayer player)
        {
            // Destroy any existing preview
            CuiHelper.DestroyUi(player, "FurnaceUIPreviewButton");
            CuiHelper.DestroyUi(player, "FurnaceUIPreviewStatus");
            
            var container = new CuiElementContainer();
            
            // Preview upgrade button
            container.Add(new CuiPanel
            {
                RectTransform = { 
                    AnchorMin = config.UISettings.UpgradeButtonAnchorMin, 
                    AnchorMax = config.UISettings.UpgradeButtonAnchorMax,
                    OffsetMin = config.UISettings.UpgradeButtonOffsetMin,
                    OffsetMax = config.UISettings.UpgradeButtonOffsetMax
                },
                Image = { Color = "0.3 0.5 0.3 0.5" } // Semi-transparent for preview
            }, "Overlay", "FurnaceUIPreviewButton");
            
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                Text = { 
                    Text = config.UISettings.UpgradeButtonText, 
                    FontSize = config.UISettings.FontSize, 
                    Align = TextAnchor.MiddleCenter, 
                    Color = config.UISettings.TextColor 
                }
            }, "FurnaceUIPreviewButton");
            
            // Preview status panel
            container.Add(new CuiPanel
            {
                RectTransform = { 
                    AnchorMin = config.UISettings.StatusPanelAnchorMin, 
                    AnchorMax = config.UISettings.StatusPanelAnchorMax,
                    OffsetMin = config.UISettings.StatusPanelOffsetMin,
                    OffsetMax = config.UISettings.StatusPanelOffsetMax
                },
                Image = { Color = "0.1 0.1 0.1 0.5" } // Semi-transparent for preview
            }, "Overlay", "FurnaceUIPreviewStatus");
            
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                Text = { 
                    Text = "Status Panel Preview", 
                    FontSize = config.UISettings.FontSize, 
                    Align = TextAnchor.MiddleCenter, 
                    Color = config.UISettings.TextColor 
                }
            }, "FurnaceUIPreviewStatus");
            
            CuiHelper.AddUi(player, container);
        }
        #endregion

        #region Commands
        private void CmdFurnaceUpgrade(BasePlayer player, string command, string[] args)
        {
            if (player == null) return;
            if (!permission.UserHasPermission(player.userID.ToString(), PERMISSION_USE))
            {
                SendReply(player, "You don't have permission to use furnace upgrades.");
                return;
            }
            
            // Debug output to help diagnose issues
            Puts($"FurnaceUpgrade command received from {player.displayName}. Args count: {args.Length}");
            if (args.Length > 0)
                Puts($"First arg: {args[0]}");
            
            // Handle "close" command
            if (args.Length > 0 && args[0] == "close")
            {
                CuiHelper.DestroyUi(player, "FurnaceUpgradeMenu");
                return;
            }
            
            // Handle upgrade command
            if (args.Length > 2 && args[0] == "upgrade")
            {
                Puts($"Processing upgrade command. Args: {string.Join(", ", args)}");
                
                // Parse furnace ID
                ulong furnaceIdNum;
                if (!ulong.TryParse(args[1], out furnaceIdNum))
                {
                    SendReply(player, $"Invalid furnace ID: {args[1]}");
                    Puts($"Failed to parse furnace ID: {args[1]}");
                    return;
                }
                
                Puts($"Parsed furnace ID: {furnaceIdNum}");
                NetworkableId furnaceId = new NetworkableId(furnaceIdNum);
                
                // Find the furnace entity
                BaseNetworkable entity = BaseNetworkable.serverEntities.Find(furnaceId);
                Puts($"Found entity: {(entity != null ? "Yes" : "No")}");
                
                BaseOven oven = entity as BaseOven;
                Puts($"Entity is BaseOven: {(oven != null ? "Yes" : "No")}");
                
                if (oven == null || !IsSupportedFurnace(oven))
                {
                    SendReply(player, "Furnace not found or not supported.");
                    Puts($"Furnace not found or not supported. ID: {furnaceIdNum}");
                    return;
                }
                
                // Check if player has permission to upgrade this furnace
                if (config.GeneralSettings.RequireOwnership && oven.OwnerID != 0 && oven.OwnerID != player.userID)
                {
                    SendReply(player, "You can only upgrade furnaces you own.");
                    return;
                }
                
                // Get upgrade type
                string upgradeType = args[2];
                if (!config.UpgradeSettings.Options.ContainsKey(upgradeType))
                {
                    SendReply(player, $"Invalid upgrade type: {upgradeType}");
                    return;
                }
                
                // Get furnace data
                if (!furnaceData.ContainsKey(furnaceId))
                {
                    InitializeFurnace(oven);
                }
                
                var data = furnaceData[furnaceId];
                
                // Calculate current level and cost
                float currentValue = 1f;
                bool isBooleanUpgrade = config.UpgradeSettings.Options[upgradeType].IsBooleanUpgrade;
                
                switch (upgradeType)
                {
                    case "smeltingspeed":
                        currentValue = data.SmeltingSpeed;
                        break;
                    case "fuelspeed":
                        currentValue = data.FuelSpeed;
                        break;
                    case "resourceoutput":
                        currentValue = data.ResourceOutput;
                        break;
                    case "charcoalmultiplier":
                        currentValue = data.CharcoalMultiplier;
                        break;
                    case "autosplit":
                        currentValue = data.AutoSplitCookables ? 2f : 1f;
                        break;
                    case "autofuel":
                        currentValue = data.AutoAddFuel ? 2f : 1f;
                        break;
                }
                
                int currentLevel;
                if (isBooleanUpgrade)
                {
                    currentLevel = currentValue > 1f ? 1 : 0;
                }
                else
                {
                    currentLevel = (int)Math.Round((currentValue - 1) * 5);
                }
                
                int maxLevel = config.UpgradeSettings.Options[upgradeType].MaxLevel;
                
                // Check if already at max level
                if (currentLevel >= maxLevel)
                {
                    SendReply(player, "This upgrade is already at maximum level.");
                    return;
                }
                
                // Calculate cost
                int cost = config.UpgradeSettings.Options[upgradeType].CostPerLevel * (currentLevel + 1);
                
                // Check if player can afford the upgrade
                bool canAfford = false;
                
                // Try Economics first
                if (Economics != null && (config.EconomySettings.PreferredEconomyPlugin == "Economics" || 
                                         config.EconomySettings.PreferredEconomyPlugin == "Both"))
                {
                    double balance = (double)Economics.Call("Balance", player.userID);
                    if (balance >= cost)
                    {
                        Economics.Call("Withdraw", player.userID, (double)cost);
                        canAfford = true;
                        Puts($"Player {player.displayName} paid {cost} via Economics");
                    }
                }
                // Try ServerRewards if Economics failed or is not preferred
                else if (ServerRewards != null && (config.EconomySettings.PreferredEconomyPlugin == "ServerRewards" || 
                                                  config.EconomySettings.PreferredEconomyPlugin == "Both"))
                {
                    int points = (int)ServerRewards.Call("CheckPoints", player.userID);
                    if (points >= cost)
                    {
                        ServerRewards.Call("TakePoints", player.userID, cost);
                        canAfford = true;
                        Puts($"Player {player.displayName} paid {cost} via ServerRewards");
                    }
                }
                
                if (!canAfford)
                {
                    SendReply(player, $"You cannot afford this upgrade. Cost: {cost} points/coins.");
                    return;
                }
                
                // Apply the upgrade
                switch (upgradeType)
                {
                    case "smeltingspeed":
                        data.SmeltingSpeed = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                        break;
                    case "fuelspeed":
                        data.FuelSpeed = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                        break;
                    case "resourceoutput":
                        data.ResourceOutput = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                        break;
                    case "charcoalmultiplier":
                        data.CharcoalMultiplier = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                        break;
                    case "autosplit":
                        data.AutoSplitCookables = true;
                        break;
                    case "autofuel":
                        data.AutoAddFuel = true;
                        break;
                }
                
                // Save data
                SaveData();
                
                // Notify player
                string message = string.Format(config.NotificationSettings.UpgradeSuccessMessage, currentLevel + 1, maxLevel);
                NotifyPlayer(player, message);
                
                // Update UI
                CuiHelper.DestroyUi(player, "FurnaceUpgradeMenu");
                ShowUpgradeMenu(player, oven);
                
                return;
            }
            
            // If no arguments or not a recognized command, show the upgrade menu
            if (args.Length > 0)
            {
                Puts($"Attempting to show upgrade menu for furnace ID: {args[0]}");
                ulong furnaceIdNum;
                if (ulong.TryParse(args[0], out furnaceIdNum))
                {
                    NetworkableId furnaceId = new NetworkableId(furnaceIdNum);
                    BaseNetworkable entity = BaseNetworkable.serverEntities.Find(furnaceId);
                    Puts($"Found entity for menu: {(entity != null ? "Yes" : "No")}");
                    
                    BaseOven oven = entity as BaseOven;
                    Puts($"Entity is BaseOven for menu: {(oven != null ? "Yes" : "No")}");
                    
                    if (oven != null && IsSupportedFurnace(oven))
                    {
                        ShowUpgradeMenu(player, oven);
                        return;
                    }
                    else
                    {
                        SendReply(player, $"Furnace not found or not supported. ID: {furnaceIdNum}");
                        Puts($"Furnace not found or not supported for menu. ID: {furnaceIdNum}");
                    }
                }
                else
                {
                    SendReply(player, $"Invalid furnace ID format: {args[0]}");
                    Puts($"Invalid furnace ID format for menu: {args[0]}");
                }
            }
            
            SendReply(player, "Usage: /furnaceupgrade <furnaceId>");
        }
        #endregion

        protected override void LoadDefaultConfig()
        {
            Config.Clear();
            Config["GeneralSettings"] = new GeneralSettings();
            Config["UISettings"] = new UISettings();
            Config["UpgradeSettings"] = new UpgradeSettings();
            Config["EconomySettings"] = new EconomySettings();
            Config["NotificationSettings"] = new NotificationSettings();
            Config["FurnaceTypeSettings"] = new FurnaceTypeSettings();
            SaveConfig();
            PrintWarning("Default configuration has been created.");
        }

        private void LoadConfig()
        {
            config = new Configuration();
            
            try
            {
                // Load GeneralSettings
                if (Config["GeneralSettings"] is Dictionary<string, object> generalData)
                {
                    config.GeneralSettings.KeepAttributesWhenRemoving = GetConfigValue(generalData, "KeepAttributesWhenRemoving", true);
                    config.GeneralSettings.RequireOwnership = GetConfigValue(generalData, "RequireOwnership", true);
                    config.GeneralSettings.EnableAutoSplitFeature = GetConfigValue(generalData, "EnableAutoSplitFeature", true);
                    config.GeneralSettings.EnableAutoFuelFeature = GetConfigValue(generalData, "EnableAutoFuelFeature", true);
                    config.GeneralSettings.MaxTotalUpgradeLevel = GetConfigValue(generalData, "MaxTotalUpgradeLevel", 20);
                }
                
                // Load UISettings
                if (Config["UISettings"] is Dictionary<string, object> uiData)
                {
                    config.UISettings.DisableStatusPanel = GetConfigValue(uiData, "DisableStatusPanel", false);
                    config.UISettings.MainColor = GetConfigValue(uiData, "MainColor", "0.1 0.1 0.1 0.9");
                    config.UISettings.ButtonColor = GetConfigValue(uiData, "ButtonColor", "0.3 0.5 0.3 0.8");
                    config.UISettings.ProgressBarColor = GetConfigValue(uiData, "ProgressBarColor", "0.2 0.8 0.2 1");
                    config.UISettings.TextColor = GetConfigValue(uiData, "TextColor", "1 1 1 1");
                    config.UISettings.FontSize = GetConfigValue(uiData, "FontSize", 14);
                    
                    // Status panel position - UPDATED DEFAULT VALUES
                    config.UISettings.StatusPanelAnchorMin = GetConfigValue(uiData, "StatusPanelAnchorMin", "0.54 0.43");
                    config.UISettings.StatusPanelAnchorMax = GetConfigValue(uiData, "StatusPanelAnchorMax", "0.94 0.51");
                    config.UISettings.StatusPanelOffsetMin = GetConfigValue(uiData, "StatusPanelOffsetMin", "0 0");
                    config.UISettings.StatusPanelOffsetMax = GetConfigValue(uiData, "StatusPanelOffsetMax", "0 0");
                    
                    // Upgrade button position - UPDATED DEFAULT VALUES
                    config.UISettings.UpgradeButtonAnchorMin = GetConfigValue(uiData, "UpgradeButtonAnchorMin", "0.7 0.03");
                    config.UISettings.UpgradeButtonAnchorMax = GetConfigValue(uiData, "UpgradeButtonAnchorMax", "0.9 0.11");
                    config.UISettings.UpgradeButtonOffsetMin = GetConfigValue(uiData, "UpgradeButtonOffsetMin", "0 0");
                    config.UISettings.UpgradeButtonOffsetMax = GetConfigValue(uiData, "UpgradeButtonOffsetMax", "0 0");
                    
                    // Upgrade menu position
                    config.UISettings.UpgradeMenuAnchorMin = GetConfigValue(uiData, "UpgradeMenuAnchorMin", "0.3 0.3");
                    config.UISettings.UpgradeMenuAnchorMax = GetConfigValue(uiData, "UpgradeMenuAnchorMax", "0.7 0.7");
                    config.UISettings.UpgradeMenuOffsetMin = GetConfigValue(uiData, "UpgradeMenuOffsetMin", "0 0");
                    config.UISettings.UpgradeMenuOffsetMax = GetConfigValue(uiData, "UpgradeMenuOffsetMax", "0 0");
                    
                    // UI text
                    config.UISettings.UpgradeButtonText = GetConfigValue(uiData, "UpgradeButtonText", "Upgrade Furnace");
                    config.UISettings.UpgradeMenuTitle = GetConfigValue(uiData, "UpgradeMenuTitle", "Furnace Upgrades");
                    config.UISettings.CloseButtonText = GetConfigValue(uiData, "CloseButtonText", "X");
                    config.UISettings.UpgradeText = GetConfigValue(uiData, "UpgradeText", "Upgrade");
                    config.UISettings.MaxLevelText = GetConfigValue(uiData, "MaxLevelText", "MAX");
                    
                    // UI positioning
                    config.UISettings.EnableDraggableUI = GetConfigValue(uiData, "EnableDraggableUI", true);
                    config.UISettings.DragStep = GetConfigValue(uiData, "DragStep", 0.01f);
                }
                
                // Load other settings...
            }
            catch (Exception ex)
            {
                PrintError($"Error loading configuration: {ex.Message}");
                LoadDefaultConfig();
            }
        }

        private void SaveConfig()
        {
            try
            {
                // Clear existing config to avoid merge issues
                Config.Clear();
                
                // Write the entire config object
                Config.WriteObject(config, true);
                
                // Debug output to verify config saving
                Puts($"Saved UI positions - Button: {config.UISettings.UpgradeButtonAnchorMin}, Status: {config.UISettings.StatusPanelAnchorMin}");
            }
            catch (Exception ex)
            {
                PrintError($"Error saving configuration: {ex.Message}");
            }
        }

        private T GetConfigValue<T>(Dictionary<string, object> data, string key, T defaultValue)
        {
            if (data.ContainsKey(key))
            {
                try
                {
                    return (T)Convert.ChangeType(data[key], typeof(T));
                }
                catch
                {
                    return defaultValue;
                }
            }
            return defaultValue;
        }

        private void NotifyPlayer(BasePlayer player, string message, string type = "info")
        {
            if (player == null) return;
            
            // Chat notification
            if (config.NotificationSettings.UseChatNotifications)
            {
                SendReply(player, message);
            }
            
            // Popup notification
            if (config.NotificationSettings.UsePopupNotifications)
            {
                player.ChatMessage(message);
            }
            
            // Notify plugin
            if (config.NotificationSettings.UseNotifyPlugin && Notify != null)
            {
                Notify.Call("SendNotify", player, message);
            }
            
            // Toastify plugin
            if (config.NotificationSettings.UseToastifyPlugin && Toastify != null)
            {
                Toastify.Call("SendToast", player, type, message);
            }
        }

        [ConsoleCommand("furnaceupgrade.debug")]
        private void ConsoleCmd_Debug(ConsoleSystem.Arg arg)
        {
            if (arg.Player() != null && !arg.Player().IsAdmin) return;
            
            Puts("=== FurnaceUpgrades Debug Info ===");
            Puts($"Registered furnaces: {furnaceData.Count}");
            
            foreach (var entry in furnaceData)
            {
                BaseNetworkable entity = BaseNetworkable.serverEntities.Find(entry.Key);
                string entityInfo = entity != null ? $"{entity.ShortPrefabName} (Owner: {entry.Value.OwnerId})" : "Not found";
                
                Puts($"Furnace ID: {entry.Key.Value}, Entity: {entityInfo}");
                Puts($"  SmeltingSpeed: {entry.Value.SmeltingSpeed}");
                Puts($"  FuelSpeed: {entry.Value.FuelSpeed}");
                Puts($"  ResourceOutput: {entry.Value.ResourceOutput}");
                Puts($"  CharcoalMultiplier: {entry.Value.CharcoalMultiplier}");
                Puts($"  AutoSplitCookables: {entry.Value.AutoSplitCookables}");
                Puts($"  AutoAddFuel: {entry.Value.AutoAddFuel}");
            }
            
            Puts("=== End Debug Info ===");
        }

        [ConsoleCommand("furnaceupgrade.menu")]
        private void ConsoleCmd_OpenMenu(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;
            
            if (arg.Args == null || arg.Args.Length < 1)
            {
                SendReply(player, "Usage: furnaceupgrade.menu <furnaceId>");
                return;
            }
            
            ulong furnaceIdNum;
            if (!ulong.TryParse(arg.Args[0], out furnaceIdNum))
            {
                SendReply(player, $"Invalid furnace ID: {arg.Args[0]}");
                return;
            }
            
            NetworkableId furnaceId = new NetworkableId(furnaceIdNum);
            BaseOven oven = BaseNetworkable.serverEntities.Find(furnaceId) as BaseOven;
            
            if (oven != null && IsSupportedFurnace(oven))
            {
                ShowUpgradeMenu(player, oven);
            }
            else
            {
                SendReply(player, $"Furnace not found or not supported. ID: {furnaceIdNum}");
            }
        }

        [ConsoleCommand("furnaceupgrade.direct")]
        private void ConsoleCmd_DirectUpgrade(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null || !player.IsAdmin) return;
            
            if (arg.Args == null || arg.Args.Length < 2)
            {
                SendReply(player, "Usage: furnaceupgrade.direct <furnaceId> <upgradeType>");
                return;
            }
            
            ulong furnaceIdNum;
            if (!ulong.TryParse(arg.Args[0], out furnaceIdNum))
            {
                SendReply(player, $"Invalid furnace ID: {arg.Args[0]}");
                return;
            }
            
            NetworkableId furnaceId = new NetworkableId(furnaceIdNum);
            BaseOven oven = BaseNetworkable.serverEntities.Find(furnaceId) as BaseOven;
            
            if (oven == null || !IsSupportedFurnace(oven))
            {
                SendReply(player, $"Furnace not found or not supported. ID: {furnaceIdNum}");
                return;
            }
            
            string upgradeType = arg.Args[1];
            if (!config.UpgradeSettings.Options.ContainsKey(upgradeType))
            {
                SendReply(player, $"Invalid upgrade type: {upgradeType}");
                return;
            }
            
            // Get furnace data
            if (!furnaceData.ContainsKey(furnaceId))
            {
                InitializeFurnace(oven);
            if (oven == null || !IsSupportedFurnace(oven))
            {
                SendReply(player, $"Furnace not found or not supported. ID: {furnaceIdNum}");
                return;
            }
            
            string upgradeType = arg.Args[1];
            if (!config.UpgradeSettings.Options.ContainsKey(upgradeType))
            {
                SendReply(player, $"Invalid upgrade type: {upgradeType}");
                return;
            }
            
            // Get furnace data
            if (!furnaceData.ContainsKey(furnaceId))
            {
                InitializeFurnace(oven);
            if (oven == null || !IsSupportedFurnace(oven))
            {
                SendReply(player, $"Furnace not found or not supported. ID: {furnaceIdNum}");
                return;
            }
            
            string upgradeType = arg.Args[1];
            if (!config.UpgradeSettings.Options.ContainsKey(upgradeType))
            {
                SendReply(player, $"Invalid upgrade type: {upgradeType}");
                return;
            }
            
            // Get furnace data
            if (!furnaceData.ContainsKey(furnaceId))
            {
                InitializeFurnace(oven);
            config = new Configuration();
            
            try
            {
                // Load GeneralSettings
                if (Config["GeneralSettings"] is Dictionary<string, object> generalData)
                {
                    config.GeneralSettings.KeepAttributesWhenRemoving = GetConfigValue(generalData, "KeepAttributesWhenRemoving", true);
                    config.GeneralSettings.RequireOwnership = GetConfigValue(generalData, "RequireOwnership", true);
                    config.GeneralSettings.EnableAutoSplitFeature = GetConfigValue(generalData, "EnableAutoSplitFeature", true);
                    config.GeneralSettings.EnableAutoFuelFeature = GetConfigValue(generalData, "EnableAutoFuelFeature", true);
                    config.GeneralSettings.MaxTotalUpgradeLevel = GetConfigValue(generalData, "MaxTotalUpgradeLevel", 20);
                }
                
                // Load UISettings
                if (Config["UISettings"] is Dictionary<string, object> uiData)
                {
                    config.UISettings.DisableStatusPanel = GetConfigValue(uiData, "DisableStatusPanel", false);
                    config.UISettings.MainColor = GetConfigValue(uiData, "MainColor", "0.1 0.1 0.1 0.9");
                    config.UISettings.ButtonColor = GetConfigValue(uiData, "ButtonColor", "0.3 0.5 0.3 0.8");
                    config.UISettings.ProgressBarColor = GetConfigValue(uiData, "ProgressBarColor", "0.2 0.8 0.2 1");
                    config.UISettings.TextColor = GetConfigValue(uiData, "TextColor", "1 1 1 1");
                    config.UISettings.FontSize = GetConfigValue(uiData, "FontSize", 14);
                    
                    // Status panel position - UPDATED DEFAULT VALUES
                    config.UISettings.StatusPanelAnchorMin = GetConfigValue(uiData, "StatusPanelAnchorMin", "0.54 0.43");
                    config.UISettings.StatusPanelAnchorMax = GetConfigValue(uiData, "StatusPanelAnchorMax", "0.94 0.51");
                    config.UISettings.StatusPanelOffsetMin = GetConfigValue(uiData, "StatusPanelOffsetMin", "0 0");
                    config.UISettings.StatusPanelOffsetMax = GetConfigValue(uiData, "StatusPanelOffsetMax", "0 0");
                    
                    // Upgrade button position - UPDATED DEFAULT VALUES
                    config.UISettings.UpgradeButtonAnchorMin = GetConfigValue(uiData, "UpgradeButtonAnchorMin", "0.7 0.03");
                    config.UISettings.UpgradeButtonAnchorMax = GetConfigValue(uiData, "UpgradeButtonAnchorMax", "0.9 0.11");
                    config.UISettings.UpgradeButtonOffsetMin = GetConfigValue(uiData, "UpgradeButtonOffsetMin", "0 0");
                    config.UISettings.UpgradeButtonOffsetMax = GetConfigValue(uiData, "UpgradeButtonOffsetMax", "0 0");
                    
                    // Upgrade menu position
                    config.UISettings.UpgradeMenuAnchorMin = GetConfigValue(uiData, "UpgradeMenuAnchorMin", "0.3 0.3");
                    config.UISettings.UpgradeMenuAnchorMax = GetConfigValue(uiData, "UpgradeMenuAnchorMax", "0.7 0.7");
                    config.UISettings.UpgradeMenuOffsetMin = GetConfigValue(uiData, "UpgradeMenuOffsetMin", "0 0");
                    config.UISettings.UpgradeMenuOffsetMax = GetConfigValue(uiData, "UpgradeMenuOffsetMax", "0 0");
                    
                    // UI text
                    config.UISettings.UpgradeButtonText = GetConfigValue(uiData, "UpgradeButtonText", "Upgrade Furnace");
                    config.UISettings.UpgradeMenuTitle = GetConfigValue(uiData, "UpgradeMenuTitle", "Furnace Upgrades");
                    config.UISettings.CloseButtonText = GetConfigValue(uiData, "CloseButtonText", "X");
                    config.UISettings.UpgradeText = GetConfigValue(uiData, "UpgradeText", "Upgrade");
                    config.UISettings.MaxLevelText = GetConfigValue(uiData, "MaxLevelText", "MAX");
                    
                    // UI positioning
                    config.UISettings.EnableDraggableUI = GetConfigValue(uiData, "EnableDraggableUI", true);
                    config.UISettings.DragStep = GetConfigValue(uiData, "DragStep", 0.01f);
                }
                
                // Load other settings...
            }
            catch (Exception ex)
            {
                PrintError($"Error loading configuration: {ex.Message}");
                LoadDefaultConfig();
            }
        }

        private void SaveConfig()
        {
            try
            {
                // Clear existing config to avoid merge issues
                Config.Clear();
                
                // Write the entire config object
                Config.WriteObject(config, true);
                
                // Debug output to verify config saving
                Puts($"Saved UI positions - Button: {config.UISettings.UpgradeButtonAnchorMin}, Status: {config.UISettings.StatusPanelAnchorMin}");
            }
            catch (Exception ex)
            {
                PrintError($"Error saving configuration: {ex.Message}");
            }
        }

        private T GetConfigValue<T>(Dictionary<string, object> data, string key, T defaultValue)
        {
            if (data.ContainsKey(key))
            {
                try
                {
                    return (T)Convert.ChangeType(data[key], typeof(T));
                }
                catch
                {
                    return defaultValue;
                }
            }
            return defaultValue;
        }

        private void NotifyPlayer(BasePlayer player, string message, string type = "info")
        {
            if (player == null) return;
            
            // Chat notification
            if (config.NotificationSettings.UseChatNotifications)
            {
                SendReply(player, message);
            }
            
            // Popup notification
            if (config.NotificationSettings.UsePopupNotifications)
            {
                player.ChatMessage(message);
            }
            
            // Notify plugin
            if (config.NotificationSettings.UseNotifyPlugin && Notify != null)
            {
                Notify.Call("SendNotify", player, message);
            }
            
            // Toastify plugin
            if (config.NotificationSettings.UseToastifyPlugin && Toastify != null)
            {
                Toastify.Call("SendToast", player, type, message);
            }
        }

        [ConsoleCommand("furnaceupgrade.debug")]
        private void ConsoleCmd_Debug(ConsoleSystem.Arg arg)
        {
            if (arg.Player() != null && !arg.Player().IsAdmin) return;
            
            Puts("=== FurnaceUpgrades Debug Info ===");
            Puts($"Registered furnaces: {furnaceData.Count}");
            
            foreach (var entry in furnaceData)
            {
                BaseNetworkable entity = BaseNetworkable.serverEntities.Find(entry.Key);
                string entityInfo = entity != null ? $"{entity.ShortPrefabName} (Owner: {entry.Value.OwnerId})" : "Not found";
                
                Puts($"Furnace ID: {entry.Key.Value}, Entity: {entityInfo}");
                Puts($"  SmeltingSpeed: {entry.Value.SmeltingSpeed}");
                Puts($"  FuelSpeed: {entry.Value.FuelSpeed}");
                Puts($"  ResourceOutput: {entry.Value.ResourceOutput}");
                Puts($"  CharcoalMultiplier: {entry.Value.CharcoalMultiplier}");
                Puts($"  AutoSplitCookables: {entry.Value.AutoSplitCookables}");
                Puts($"  AutoAddFuel: {entry.Value.AutoAddFuel}");
            }
            
            Puts("=== End Debug Info ===");
        }

        [ConsoleCommand("furnaceupgrade.menu")]
        private void ConsoleCmd_OpenMenu(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;
            
            if (arg.Args == null || arg.Args.Length < 1)
            {
                SendReply(player, "Usage: furnaceupgrade.menu <furnaceId>");
                return;
            }
            
            ulong furnaceIdNum;
            if (!ulong.TryParse(arg.Args[0], out furnaceIdNum))
            {
                SendReply(player, $"Invalid furnace ID: {arg.Args[0]}");
                return;
            }
            
            NetworkableId furnaceId = new NetworkableId(furnaceIdNum);
            BaseOven oven = BaseNetworkable.serverEntities.Find(furnaceId) as BaseOven;
            
            if (oven != null && IsSupportedFurnace(oven))
            {
                ShowUpgradeMenu(player, oven);
            }
            else
            {
                SendReply(player, $"Furnace not found or not supported. ID: {furnaceIdNum}");
            }
        }

        [ConsoleCommand("furnaceupgrade.direct")]
        private void ConsoleCmd_DirectUpgrade(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null || !player.IsAdmin) return;
            
            if (arg.Args == null || arg.Args.Length < 2)
            {
                SendReply(player, "Usage: furnaceupgrade.direct <furnaceId> <upgradeType>");
                return;
            }
            
            ulong furnaceIdNum;
            if (!ulong.TryParse(arg.Args[0], out furnaceIdNum))
            {
                SendReply(player, $"Invalid furnace ID: {arg.Args[0]}");
                return;
            }
            
            NetworkableId furnaceId = new NetworkableId(furnaceIdNum);
            BaseOven oven = BaseNetworkable.serverEntities.Find(furnaceId) as BaseOven;
            
            if (oven == null || !IsSupportedFurnace(oven))
            {
                SendReply(player, $"Furnace not found or not supported. ID: {furnaceIdNum}");
                return;
            }
            
            string upgradeType = arg.Args[1];
            if (!config.UpgradeSettings.Options.ContainsKey(upgradeType))
            {
                SendReply(player, $"Invalid upgrade type: {upgradeType}");
                return;
            }
            
            // Get furnace data
            if (!furnaceData.ContainsKey(furnaceId))
            {
                InitializeFurnace(oven);
                    Text = "Upgrade Button Preview", 
                    FontSize = config.UISettings.FontSize, 
                    Align = TextAnchor.MiddleCenter, 
                    Color = config.UISettings.TextColor 
                }
            }, "FurnaceUIPreviewButton");
            
            // Preview status panel
            container.Add(new CuiPanel
            {
                RectTransform = { 
                    AnchorMin = config.UISettings.StatusPanelAnchorMin, 
                    AnchorMax = config.UISettings.StatusPanelAnchorMax,
                    OffsetMin = config.UISettings.StatusPanelOffsetMin,
                    OffsetMax = config.UISettings.StatusPanelOffsetMax
                },
                Image = { Color = "0.1 0.1 0.1 0.5" } // Semi-transparent for preview
            }, "Overlay", "FurnaceUIPreviewStatus");
            
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                Text = { 
                    Text = "Status Panel Preview", 
                    FontSize = config.UISettings.FontSize, 
                    Align = TextAnchor.MiddleCenter, 
                    Color = config.UISettings.TextColor 
                }
            }, "FurnaceUIPreviewStatus");
            
            CuiHelper.AddUi(player, container);
            
            // Auto-destroy preview after 10 seconds
            timer.Once(10f, () => {
                if (player != null && player.IsConnected)
                {
                    CuiHelper.DestroyUi(player, "FurnaceUIPreviewButton");
                    CuiHelper.DestroyUi(player, "FurnaceUIPreviewStatus");
                }
            });
        }

        [ConsoleCommand("furnaceupgrade.close")]
        private void ConsoleCmd_CloseMenu(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;
            
            CuiHelper.DestroyUi(player, "FurnaceUpgradeMenu");
        }

        [ChatCommand("furnacereload")]
        private void CmdReloadConfig(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, "furnaceupgrades.admin"))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            // Save current positions before reload for debugging
            string oldButtonPos = config.UISettings.UpgradeButtonAnchorMin;
            string oldStatusPos = config.UISettings.StatusPanelAnchorMin;
            
            // Reload the config
            LoadConfig();
            
            // Check if positions were preserved
            SendReply(player, $"Config reloaded. Button position: {oldButtonPos} -> {config.UISettings.UpgradeButtonAnchorMin}");
            SendReply(player, $"Status position: {oldStatusPos} -> {config.UISettings.StatusPanelAnchorMin}");
        }

        [ChatCommand("furnacesetpos")]
        private void CmdSetPosition(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, "furnaceupgrades.admin"))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            if (args.Length < 3)
            {
                SendReply(player, "Usage: /furnacesetpos <button|status> <minX> <minY> <maxX> <maxY>");
                return;
            }
            
            string element = args[0].ToLower();
            
            try
            {
                float minX = float.Parse(args[1]);
                float minY = float.Parse(args[2]);
                float maxX = args.Length > 3 ? float.Parse(args[3]) : minX + 0.2f;
                float maxY = args.Length > 4 ? float.Parse(args[4]) : minY + 0.08f;
                
                // Ensure values are within bounds
                minX = Mathf.Clamp01(minX);
                minY = Mathf.Clamp01(minY);
                maxX = Mathf.Clamp01(maxX);
                maxY = Mathf.Clamp01(maxY);
                
                if (element == "button")
                {
                    config.UISettings.UpgradeButtonAnchorMin = $"{minX} {minY}";
                    config.UISettings.UpgradeButtonAnchorMax = $"{maxX} {maxY}";
                }
                else if (element == "status")
                {
                    config.UISettings.StatusPanelAnchorMin = $"{minX} {minY}";
                    config.UISettings.StatusPanelAnchorMax = $"{maxX} {maxY}";
                }
                else
                {
                    SendReply(player, "Invalid element. Use 'button' or 'status'.");
                    return;
                }
                
                // Save the config
                SaveConfig();
                
                // Show the new positions
                SendReply(player, $"Set {element} position to Min={minX} {minY}, Max={maxX} {maxY}");
                ShowUIPreview(player);
            }
            catch (Exception ex)
            {
                SendReply(player, $"Error setting position: {ex.Message}");
            }
        }

        // Add a method to dump the current config to console for debugging
        [ConsoleCommand("furnaceconfig.dump")]
        private void ConsoleCmdDumpConfig(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player != null && !permission.UserHasPermission(player.UserIDString, "furnaceupgrades.admin"))
                return;
            
            // Dump the current config to console
            Puts("Current Furnace Upgrades Configuration:");
            Puts($"Button Position: Min={config.UISettings.UpgradeButtonAnchorMin}, Max={config.UISettings.UpgradeButtonAnchorMax}");
            Puts($"Status Position: Min={config.UISettings.StatusPanelAnchorMin}, Max={config.UISettings.StatusPanelAnchorMax}");
            
            // If player issued the command, also send to chat
            if (player != null)
            {
                SendReply(player, "Configuration dumped to console. Check server console for details.");
            }
        }

        [ChatCommand("furnaceinfo")]
        private void CmdFurnaceInfo(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, "furnaceupgrades.admin"))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            RaycastHit hit;
            if (Physics.Raycast(player.eyes.HeadRay(), out hit, 3f))
            {
                BaseOven oven = hit.GetEntity() as BaseOven;
                if (oven != null && IsSupportedFurnace(oven))
                {
                    SendReply(player, $"Furnace ID: {oven.net.ID.Value}");
                    
                    if (furnaceData.ContainsKey(oven.net.ID))
                    {
                        var data = furnaceData[oven.net.ID];
                        SendReply(player, $"Owner ID: {data.OwnerId}");
                        SendReply(player, $"Smelting Speed: {data.SmeltingSpeed}x");
                        SendReply(player, $"Fuel Speed: {data.FuelSpeed}x");
                        SendReply(player, $"Resource Output: {data.ResourceOutput}x");
                        SendReply(player, $"Charcoal Multiplier: {data.CharcoalMultiplier}x");
                        SendReply(player, $"Auto Split: {data.AutoSplitCookables}");
                        SendReply(player, $"Auto Fuel: {data.AutoAddFuel}");
                    }
                    else
                    {
                        SendReply(player, "No data found for this furnace.");
                    }
                }
                else
                {
                    SendReply(player, "You must be looking at a furnace.");
                }
            }
            else
            {
                SendReply(player, "You must be looking at a furnace.");
            }
        }

        [ConsoleCommand("furnaceupgrade.doupgrade")]
        private void ConsoleCmd_DoUpgrade(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;
            
            Puts($"Upgrade command received from {player.displayName}. Args: {arg.Args?.Length ?? 0}");
            
            if (!permission.UserHasPermission(player.userID.ToString(), PERMISSION_USE))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            // Check if we have enough arguments
            if (arg.Args == null || arg.Args.Length < 2)
            {
                SendReply(player, "Usage: furnaceupgrade.doupgrade <furnaceId> <upgradeType>");
                Puts("Not enough arguments for upgrade command");
                return;
            }
            
            // Parse furnace ID
            ulong furnaceIdNum;
            if (!ulong.TryParse(arg.Args[0], out furnaceIdNum))
            {
                SendReply(player, $"Invalid furnace ID: {arg.Args[0]}");
                Puts($"Invalid furnace ID: {arg.Args[0]}");
                return;
            }
            
            NetworkableId furnaceId = new NetworkableId(furnaceIdNum);
            BaseOven oven = BaseNetworkable.serverEntities.Find(furnaceId) as BaseOven;
            
            if (oven == null || !IsSupportedFurnace(oven))
            {
                SendReply(player, "Furnace not found or not supported.");
                Puts($"Furnace not found or not supported. ID: {furnaceIdNum}");
                return;
            }
            
            string upgradeType = arg.Args[1].ToLower();
            if (!config.UpgradeSettings.Options.ContainsKey(upgradeType))
            {
                SendReply(player, $"Invalid upgrade type: {upgradeType}");
                Puts($"Invalid upgrade type: {upgradeType}");
                return;
            }
            
            Puts($"Processing upgrade: {upgradeType} for furnace {furnaceIdNum}");
            
            // Get furnace data
            if (!furnaceData.ContainsKey(furnaceId))
            {
                InitializeFurnace(oven);
            }
            
            var data = furnaceData[furnaceId];
            
            // Calculate current level and cost
            float currentValue = 1f;
            bool isBooleanUpgrade = config.UpgradeSettings.Options[upgradeType].IsBooleanUpgrade;
            
            switch (upgradeType)
            {
                case "smeltingspeed":
                    currentValue = data.SmeltingSpeed;
                    break;
                case "fuelspeed":
                    currentValue = data.FuelSpeed;
                    break;
                case "resourceoutput":
                    currentValue = data.ResourceOutput;
                    break;
                case "charcoalmultiplier":
                    currentValue = data.CharcoalMultiplier;
                    break;
                case "autosplit":
                    currentValue = data.AutoSplitCookables ? 2f : 1f;
                    break;
                case "autofuel":
                    currentValue = data.AutoAddFuel ? 2f : 1f;
                    break;
            }
            
            int currentLevel;
            if (isBooleanUpgrade)
            {
                currentLevel = currentValue > 1f ? 1 : 0;
            }
            else
            {
                currentLevel = (int)Math.Round((currentValue - 1) * 5);
            }
            
            Puts($"Current level for {upgradeType}: {currentLevel}");
            
            // Check if already at max level
            int maxLevel = config.UpgradeSettings.Options[upgradeType].MaxLevel;
            if (currentLevel >= maxLevel)
            {
                SendReply(player, $"This upgrade is already at maximum level ({maxLevel}).");
                Puts($"Upgrade {upgradeType} already at max level {maxLevel}");
                return;
            }
            
            // Calculate cost
            int cost = config.UpgradeSettings.Options[upgradeType].CostPerLevel * (currentLevel + 1);
            
            // Check if player can afford the upgrade
            bool canAfford = false;
            
            // Try Economics first
            if (Economics != null && (config.EconomySettings.PreferredEconomyPlugin == "Economics" || 
                                     config.EconomySettings.PreferredEconomyPlugin == "Both"))
            {
                double balance = (double)Economics.Call("Balance", player.userID);
                Puts($"Economics balance: {balance}, cost: {cost}");
                if (balance >= cost)
                {
                    Economics.Call("Withdraw", player.userID, (double)cost);
                    canAfford = true;
                    Puts($"Player {player.displayName} paid {cost} via Economics");
                }
            }
            
            // Try ServerRewards if Economics failed or is not preferred
            if (!canAfford && ServerRewards != null && (config.EconomySettings.PreferredEconomyPlugin == "ServerRewards" || 
                                                      config.EconomySettings.PreferredEconomyPlugin == "Both"))
            {
                int balance = (int)ServerRewards.Call("CheckPoints", player.userID);
                Puts($"ServerRewards balance: {balance}, cost: {cost}");
                if (balance >= cost)
                {
                    ServerRewards.Call("TakePoints", player.userID, cost);
                    canAfford = true;
                    Puts($"Player {player.displayName} paid {cost} via ServerRewards");
                }
            }
            
            // For testing, let's make it free if no economy plugins are available
            if (!canAfford && Economics == null && ServerRewards == null)
            {
                Puts("No economy plugins available, making upgrade free for testing");
                canAfford = true;
            }
            
            if (!canAfford)
            {
                SendReply(player, $"You cannot afford this upgrade. Cost: {cost}");
                Puts($"Player cannot afford upgrade. Cost: {cost}");
                return;
            }
            
            // Apply the upgrade
            Puts($"Applying upgrade {upgradeType} for player {player.displayName}");
            
            switch (upgradeType)
            {
                case "smeltingspeed":
                    data.SmeltingSpeed = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New smelting speed: {data.SmeltingSpeed}");
                    break;
                case "fuelspeed":
                    data.FuelSpeed = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New fuel speed: {data.FuelSpeed}");
                    break;
                case "resourceoutput":
                    data.ResourceOutput = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New resource output: {data.ResourceOutput}");
                    break;
                case "charcoalmultiplier":
                    data.CharcoalMultiplier = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New charcoal multiplier: {data.CharcoalMultiplier}");
                    break;
                case "autosplit":
                    data.AutoSplitCookables = true;
                    Puts("Auto split enabled");
                    break;
                case "autofuel":
                    data.AutoAddFuel = true;
                    Puts("Auto fuel enabled");
                    break;
            }
            
            // Save data
            SaveData();
            
            // Notify player
            string message = string.Format(config.NotificationSettings.UpgradeSuccessMessage, currentLevel + 1, maxLevel);
            NotifyPlayer(player, message);
            
            // Update UI
            CuiHelper.DestroyUi(player, "FurnaceUpgradeMenu");
            ShowUpgradeMenu(player, oven);
        }
    }
}
                    Puts($"New smelting speed: {data.SmeltingSpeed}");
                    break;
                case "fuelspeed":
                    data.FuelSpeed = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New fuel speed: {data.FuelSpeed}");
                    break;
                case "resourceoutput":
                    data.ResourceOutput = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New resource output: {data.ResourceOutput}");
                    break;
                case "charcoalmultiplier":
                    data.CharcoalMultiplier = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New charcoal multiplier: {data.CharcoalMultiplier}");
                    break;
                case "autosplit":
                    data.AutoSplitCookables = true;
                    Puts("Auto split enabled");
                    break;
                case "autofuel":
                    data.AutoAddFuel = true;
                    Puts("Auto fuel enabled");
                    break;
            }
            
            // Save data
            SaveData();
            
            // Notify player
            string message = string.Format(config.NotificationSettings.UpgradeSuccessMessage, currentLevel + 1, maxLevel);
            NotifyPlayer(player, message);
            
            // Update UI
            CuiHelper.DestroyUi(player, "FurnaceUpgradeMenu");
            ShowUpgradeMenu(player, oven);
        }
    }
}
                    data.CharcoalMultiplier = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New charcoal multiplier: {data.CharcoalMultiplier}");
                    break;
                case "autosplit":
                    data.AutoSplitCookables = true;
                    Puts("Auto split enabled");
                    break;
                case "autofuel":
                    data.AutoAddFuel = true;
                    Puts("Auto fuel enabled");
                    break;
            }
            
            // Save data
            SaveData();
            
            // Notify player
            string message = string.Format(config.NotificationSettings.UpgradeSuccessMessage, currentLevel + 1, maxLevel);
            NotifyPlayer(player, message);
            
            // Update UI
            CuiHelper.DestroyUi(player, "FurnaceUpgradeMenu");
            ShowUpgradeMenu(player, oven);
        }
    }
}
            BaseOven oven = BaseNetworkable.serverEntities.Find(furnaceId) as BaseOven;
            
            if (oven == null || !IsSupportedFurnace(oven))
            {
                SendReply(player, "Furnace not found or not supported.");
                Puts($"Furnace not found or not supported. ID: {furnaceIdNum}");
                return;
            }
            
            string upgradeType = arg.Args[1].ToLower();
            if (!config.UpgradeSettings.Options.ContainsKey(upgradeType))
            {
                SendReply(player, $"Invalid upgrade type: {upgradeType}");
                Puts($"Invalid upgrade type: {upgradeType}");
                return;
            }
            
            Puts($"Processing upgrade: {upgradeType} for furnace {furnaceIdNum}");
            
            // Get furnace data
            if (!furnaceData.ContainsKey(furnaceId))
            {
                InitializeFurnace(oven);
            }
            
            var data = furnaceData[furnaceId];
            
            // Calculate current level and cost
            float currentValue = 1f;
            bool isBooleanUpgrade = config.UpgradeSettings.Options[upgradeType].IsBooleanUpgrade;
            
            switch (upgradeType)
            {
                case "smeltingspeed":
                    currentValue = data.SmeltingSpeed;
                    break;
                case "fuelspeed":
                    currentValue = data.FuelSpeed;
                    break;
                case "resourceoutput":
                    currentValue = data.ResourceOutput;
                    break;
                case "charcoalmultiplier":
                    currentValue = data.CharcoalMultiplier;
                    break;
                case "autosplit":
                    currentValue = data.AutoSplitCookables ? 2f : 1f;
                    break;
                case "autofuel":
                    currentValue = data.AutoAddFuel ? 2f : 1f;
                    break;
            }
            
            int currentLevel;
            if (isBooleanUpgrade)
            {
                currentLevel = currentValue > 1f ? 1 : 0;
            }
            else
            {
                currentLevel = (int)Math.Round((currentValue - 1) * 5);
            }
            
            Puts($"Current level for {upgradeType}: {currentLevel}");
            
            // Check if already at max level
            int maxLevel = config.UpgradeSettings.Options[upgradeType].MaxLevel;
            if (currentLevel >= maxLevel)
            {
                SendReply(player, $"This upgrade is already at maximum level ({maxLevel}).");
                Puts($"Upgrade {upgradeType} already at max level {maxLevel}");
                return;
            }
            
            // Calculate cost
            int cost = config.UpgradeSettings.Options[upgradeType].CostPerLevel * (currentLevel + 1);
            
            // Check if player can afford the upgrade
            bool canAfford = false;
            
            // Try Economics first
            if (Economics != null && (config.EconomySettings.PreferredEconomyPlugin == "Economics" || 
                                     config.EconomySettings.PreferredEconomyPlugin == "Both"))
            {
                double balance = (double)Economics.Call("Balance", player.userID);
                Puts($"Economics balance: {balance}, cost: {cost}");
                if (balance >= cost)
                {
                    Economics.Call("Withdraw", player.userID, (double)cost);
                    canAfford = true;
                    Puts($"Player {player.displayName} paid {cost} via Economics");
                }
            }
            
            // Try ServerRewards if Economics failed or is not preferred
            if (!canAfford && ServerRewards != null && (config.EconomySettings.PreferredEconomyPlugin == "ServerRewards" || 
                                                      config.EconomySettings.PreferredEconomyPlugin == "Both"))
            {
                int balance = (int)ServerRewards.Call("CheckPoints", player.userID);
                Puts($"ServerRewards balance: {balance}, cost: {cost}");
                if (balance >= cost)
                {
                    ServerRewards.Call("TakePoints", player.userID, cost);
                    canAfford = true;
                    Puts($"Player {player.displayName} paid {cost} via ServerRewards");
                }
            }
            
            // For testing, let's make it free if no economy plugins are available
            if (!canAfford && Economics == null && ServerRewards == null)
            {
                Puts("No economy plugins available, making upgrade free for testing");
                canAfford = true;
            }
            
            if (!canAfford)
            {
                SendReply(player, $"You cannot afford this upgrade. Cost: {cost}");
                Puts($"Player cannot afford upgrade. Cost: {cost}");
                return;
            }
            
            // Apply the upgrade
            Puts($"Applying upgrade {upgradeType} for player {player.displayName}");
            
            switch (upgradeType)
            {
                case "smeltingspeed":
                    data.SmeltingSpeed = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New smelting speed: {data.SmeltingSpeed}");
                    break;
                case "fuelspeed":
                    data.FuelSpeed = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New fuel speed: {data.FuelSpeed}");
                    break;
                case "resourceoutput":
                    data.ResourceOutput = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New resource output: {data.ResourceOutput}");
                    break;
                case "charcoalmultiplier":
                    data.CharcoalMultiplier = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New charcoal multiplier: {data.CharcoalMultiplier}");
                    break;
                case "autosplit":
                    data.AutoSplitCookables = true;
                    Puts("Auto split enabled");
                    break;
                case "autofuel":
                    data.AutoAddFuel = true;
                    Puts("Auto fuel enabled");
                    break;
            }
            
            // Save data
            SaveData();
            
            // Notify player
            string message = string.Format(config.NotificationSettings.UpgradeSuccessMessage, currentLevel + 1, maxLevel);
            NotifyPlayer(player, message);
            
            // Update UI
            CuiHelper.DestroyUi(player, "FurnaceUpgradeMenu");
            ShowUpgradeMenu(player, oven);
        }
    }
}
            int currentLevel;
            if (isBooleanUpgrade)
            {
                currentLevel = currentValue > 1f ? 1 : 0;
            }
            else
            {
                currentLevel = (int)Math.Round((currentValue - 1) * 5);
            }
            
            Puts($"Current level for {upgradeType}: {currentLevel}");
            
            // Check if already at max level
            int maxLevel = config.UpgradeSettings.Options[upgradeType].MaxLevel;
            if (currentLevel >= maxLevel)
            {
                SendReply(player, $"This upgrade is already at maximum level ({maxLevel}).");
                Puts($"Upgrade {upgradeType} already at max level {maxLevel}");
                return;
            }
            
            // Calculate cost
            int cost = config.UpgradeSettings.Options[upgradeType].CostPerLevel * (currentLevel + 1);
            
            // Check if player can afford the upgrade
            bool canAfford = false;
            
            // Try Economics first
            if (Economics != null && (config.EconomySettings.PreferredEconomyPlugin == "Economics" || 
                                     config.EconomySettings.PreferredEconomyPlugin == "Both"))
            {
                double balance = (double)Economics.Call("Balance", player.userID);
                Puts($"Economics balance: {balance}, cost: {cost}");
                if (balance >= cost)
                {
                    Economics.Call("Withdraw", player.userID, (double)cost);
                    canAfford = true;
                    Puts($"Player {player.displayName} paid {cost} via Economics");
                }
            }
            
            // Try ServerRewards if Economics failed or is not preferred
            if (!canAfford && ServerRewards != null && (config.EconomySettings.PreferredEconomyPlugin == "ServerRewards" || 
                                                      config.EconomySettings.PreferredEconomyPlugin == "Both"))
            {
                int balance = (int)ServerRewards.Call("CheckPoints", player.userID);
                Puts($"ServerRewards balance: {balance}, cost: {cost}");
                if (balance >= cost)
                {
                    ServerRewards.Call("TakePoints", player.userID, cost);
                    canAfford = true;
                    Puts($"Player {player.displayName} paid {cost} via ServerRewards");
                }
            }
            
            // For testing, let's make it free if no economy plugins are available
            if (!canAfford && Economics == null && ServerRewards == null)
            {
                Puts("No economy plugins available, making upgrade free for testing");
                canAfford = true;
            }
            
            if (!canAfford)
            {
                SendReply(player, $"You cannot afford this upgrade. Cost: {cost}");
                Puts($"Player cannot afford upgrade. Cost: {cost}");
                return;
            }
            
            // Apply the upgrade
            Puts($"Applying upgrade {upgradeType} for player {player.displayName}");
            
            switch (upgradeType)
            {
                case "smeltingspeed":
                    data.SmeltingSpeed = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New smelting speed: {data.SmeltingSpeed}");
                    break;
                case "fuelspeed":
                    data.FuelSpeed = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New fuel speed: {data.FuelSpeed}");
                    break;
                case "resourceoutput":
                    data.ResourceOutput = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New resource output: {data.ResourceOutput}");
                    break;
                case "charcoalmultiplier":
                    data.CharcoalMultiplier = 1f + ((currentLevel + 1) * config.UpgradeSettings.Options[upgradeType].ValueMultiplierPerLevel);
                    Puts($"New charcoal multiplier: {data.CharcoalMultiplier}");
                    break;
                case "autosplit":
                    data.AutoSplitCookables = true;
                    Puts("Auto split enabled");
                    break;
                case "autofuel":
                    data.AutoAddFuel = true;
                    Puts("Auto fuel enabled");
                    break;
            }
            
            // Save data
            SaveData();
            
            // Notify player
            string message = string.Format(config.NotificationSettings.UpgradeSuccessMessage, currentLevel + 1, maxLevel);
            NotifyPlayer(player, message);
            
            // Update UI
            CuiHelper.DestroyUi(player, "FurnaceUpgradeMenu");
            ShowUpgradeMenu(player, oven);
        }

        [ChatCommand("furnaceinfo")]
        private void CmdFurnaceInfo(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, "furnaceupgrades.admin"))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            RaycastHit hit;
            if (Physics.Raycast(player.eyes.HeadRay(), out hit, 3f))
            {
                BaseOven oven = hit.GetEntity() as BaseOven;
                if (oven != null && IsSupportedFurnace(oven))
                {
                    SendReply(player, $"Furnace ID: {oven.net.ID.Value}");
                    
                    if (furnaceData.ContainsKey(oven.net.ID))
                    {
                        var data = furnaceData[oven.net.ID];
                        SendReply(player, $"Owner ID: {data.OwnerId}");
                        SendReply(player, $"Smelting Speed: {data.SmeltingSpeed}x");
                        SendReply(player, $"Fuel Speed: {data.FuelSpeed}x");
                        SendReply(player, $"Resource Output: {data.ResourceOutput}x");
                        SendReply(player, $"Charcoal Multiplier: {data.CharcoalMultiplier}x");
                        SendReply(player, $"Auto Split: {data.AutoSplitCookables}");
                        SendReply(player, $"Auto Fuel: {data.AutoAddFuel}");
                    }
                    else
                    {
                        SendReply(player, "No data found for this furnace.");
                    }
                }
                else
                {
                    SendReply(player, "You must be looking at a furnace.");
                }
            }
            else
            {
                SendReply(player, "You must be looking at a furnace.");
            }
        }

        // Add a method to check if a furnace is supported
        private bool IsSupportedFurnace(BaseOven oven)
        {
            if (oven == null) return false;
            
            // Check if this is a supported furnace type
            string prefabName = oven.ShortPrefabName;
            Puts($"Checking furnace type: {prefabName}");
            
            // Default to supporting standard furnaces
            return prefabName.Contains("furnace");
        }
    }
}


























