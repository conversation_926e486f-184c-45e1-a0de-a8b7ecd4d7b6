using System;
using uMod.Common;
using System.Collections.Generic;
using System.Linq;

namespace uMod.Plugins
{
	[Info("Server Restarter", "klauz24", "1.1.0"), Description("Automatically restarts your server at configured times with advanced options.")]
	internal class ServerRestarter : Plugin
	{
		private Timer _timer;
		private Configuration _config;
		private Dictionary<int, Timer> _warningTimers = new Dictionary<int, Timer>();

		[Config, Toml]
		private class Configuration
		{
			public bool EnableDailyRestart = true;
			public string DailyRestartTime = "06:00";
			public List<string> AdditionalRestartTimes = new List<string> { "12:00", "18:00", "00:00" };
			public int RestartDelay = 60;
			public Dictionary<int, string> WarningMessages = new Dictionary<int, string>
			{
				[30] = "Server will restart in 30 minutes",
				[15] = "Server will restart in 15 minutes",
				[10] = "Server will restart in 10 minutes",
				[5] = "Server will restart in 5 minutes",
				[1] = "Server will restart in 1 minute"
			};
			public string FinalRestartMessage = "Server is restarting now!";
			public bool EnableDiscordNotifications = false;
			public string DiscordWebhookUrl = "";
		}

		[Locale, Toml]
		private class PluginLang : Localization
		{
			public string Message => "Server restart in {0} seconds, get in a safe location.";
			public string WarningMessage => "{0}";
			public string FinalMessage => "{0}";
		}

		[Localization]
		private interface Localization : ILocale
		{
			string Message { get; }
			string WarningMessage { get; }
			string FinalMessage { get; }
		}

		private void Loaded(Configuration config) => _config = config;

		private void OnServerInitialized(bool initial)
		{
			// Start checking for restart times every minute
			_timer = timer.Every(60f, CheckRestartTimes);
			
			Puts("Server Restarter initialized!");
			
			// Combine all restart times for logging
			List<string> allRestartTimes = new List<string>();
			if (_config.EnableDailyRestart)
				allRestartTimes.Add(_config.DailyRestartTime);
			allRestartTimes.AddRange(_config.AdditionalRestartTimes);
			
			Puts($"Configured restart times: {string.Join(", ", allRestartTimes)}");
		}

		private void Unload()
		{
			// Clean up timers
			_timer?.Destroy();
			foreach (var timer in _warningTimers.Values)
				timer?.Destroy();
			_warningTimers.Clear();
		}

		private void CheckRestartTimes()
		{
			DateTime now = DateTime.Now;
			string currentTime = now.ToString("HH:mm");
			
			// Get all active restart times
			List<string> allRestartTimes = new List<string>();
			if (_config.EnableDailyRestart)
				allRestartTimes.Add(_config.DailyRestartTime);
			allRestartTimes.AddRange(_config.AdditionalRestartTimes);
			
			// Check if current time matches any restart time
			if (allRestartTimes.Contains(currentTime))
			{
				// Schedule the restart
				ScheduleRestart();
			}
			
			// Check for upcoming restarts to set warning timers
			foreach (string restartTime in allRestartTimes)
			{
				DateTime restartDateTime = ParseRestartTime(restartTime);
				
				// If restart time is in the future and within warning range
				if (restartDateTime > now)
				{
					TimeSpan timeUntilRestart = restartDateTime - now;
					
					// Set warning timers if they're not already set
					foreach (int warningMinute in _config.WarningMessages.Keys)
					{
						if (timeUntilRestart.TotalMinutes <= warningMinute && 
							timeUntilRestart.TotalMinutes > warningMinute - 1 && 
							!_warningTimers.ContainsKey(warningMinute))
						{
							float delaySeconds = (float)(timeUntilRestart.TotalMinutes - warningMinute) * 60f;
							_warningTimers[warningMinute] = timer.Once(delaySeconds, () => 
							{
								BroadcastWarning(_config.WarningMessages[warningMinute]);
								_warningTimers.Remove(warningMinute);
							});
						}
					}
				}
			}
		}
		
		private DateTime ParseRestartTime(string timeString)
		{
			string[] timeParts = timeString.Split(':');
			int hours = int.Parse(timeParts[0]);
			int minutes = int.Parse(timeParts[1]);
			
			DateTime now = DateTime.Now;
			DateTime restartTime = new DateTime(now.Year, now.Month, now.Day, hours, minutes, 0);
			
			// If the time has already passed today, set it for tomorrow
			if (restartTime < now)
				restartTime = restartTime.AddDays(1);
				
			return restartTime;
		}
		
		private void ScheduleRestart()
		{
			// Broadcast final message
			BroadcastWarning(_config.FinalRestartMessage);
			
			// Send Discord notification if enabled
			if (_config.EnableDiscordNotifications && !string.IsNullOrEmpty(_config.DiscordWebhookUrl))
				SendDiscordNotification(_config.FinalRestartMessage);
			
			// Execute restart after the configured delay
			timer.In(_config.RestartDelay, () => Server.Shutdown());
		}
		
		private void BroadcastWarning(string message)
		{
			Puts($"Broadcasting: {message}");
			
			foreach (var player in Players.Connected)
			{
				player.Message(string.Format(Locale<PluginLang>(player).WarningMessage, message));
			}
		}
		
		private void SendDiscordNotification(string message)
		{
			// Implementation would depend on uMod's web request capabilities
			// This is a placeholder based on the Oxide implementation
			// You may need to adjust this based on uMod's actual API
			webrequest.Enqueue(_config.DiscordWebhookUrl, 
				$"{{\"content\":\"{message}\"}}", 
				(code, response) => {}, 
				this, RequestMethod.POST, 
				new Dictionary<string, string> { ["Content-Type"] = "application/json" });
		}
	}
}

