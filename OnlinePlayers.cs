using System;
using System.Collections.Generic;
using System.Linq;
using Oxide.Core;
using Oxide.Core.Plugins;
using UnityEngine;
using Newtonsoft.Json;

namespace Oxide.Plugins
{
    [Info("Online Players", "YourName", "1.1.0")]
    [Description("Displays a list of online players and sleepers when using the /online command")]
    class OnlinePlayers : RustPlugin
    {
        #region Configuration
        
        private Configuration config;
        
        private class Configuration
        {
            [JsonProperty("Show sleepers to admins only")]
            public bool ShowSleepersToAdminsOnly = true;
            
            [JsonProperty("Online players header color")]
            public string OnlineHeaderColor = "#7FFF00";
            
            [JsonProperty("Sleepers header color")]
            public string SleepersHeaderColor = "#FFA500";
            
            [JsonProperty("Player name color")]
            public string PlayerNameColor = "#FFFFFF";
            
            [JsonProperty("Max players to show per page")]
            public int MaxPlayersPerPage = 20;
            
            [JsonProperty("Enable pagination")]
            public bool EnablePagination = true;
        }
        
        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
            }
            catch
            {
                PrintError("Configuration file is corrupt! Loading default configuration...");
                LoadDefaultConfig();
            }
            SaveConfig();
        }
        
        protected override void LoadDefaultConfig()
        {
            config = new Configuration();
        }
        
        protected override void SaveConfig()
        {
            Config.WriteObject(config);
        }
        
        #endregion
        
        #region Hooks
        
        void OnServerInitialized()
        {
            // Register command
            cmd.AddChatCommand("online", this, "CmdOnlinePlayers");
            
            // Register permission
            permission.RegisterPermission("onlineplayers.admin", this);
            
            Puts("Online Players plugin initialized!");
        }
        
        #endregion
        
        #region Commands
        
        private void CmdOnlinePlayers(BasePlayer player, string command, string[] args)
        {
            if (player == null) return;
            
            bool isAdmin = permission.UserHasPermission(player.UserIDString, "onlineplayers.admin");
            int page = 1;
            
            // Parse page number if pagination is enabled
            if (config.EnablePagination && args.Length > 0)
            {
                if (!int.TryParse(args[0], out page))
                    page = 1;
                
                if (page < 1) page = 1;
            }
            
            // Get list of online players
            var onlinePlayers = BasePlayer.activePlayerList;
            
            // Create message header
            string message = $"<color={config.OnlineHeaderColor}>Online Players ({onlinePlayers.Count}):</color>\n";
            
            // Add online players to the message with pagination
            if (config.EnablePagination)
            {
                int startIndex = (page - 1) * config.MaxPlayersPerPage;
                int endIndex = Math.Min(startIndex + config.MaxPlayersPerPage, onlinePlayers.Count);
                int totalPages = (int)Math.Ceiling((double)onlinePlayers.Count / config.MaxPlayersPerPage);
                
                if (startIndex >= onlinePlayers.Count && page > 1)
                {
                    SendReply(player, $"Page {page} does not exist. Total pages: {totalPages}");
                    return;
                }
                
                message += $"<color=#AAAAAA>Page {page}/{totalPages}</color>\n";
                
                for (int i = startIndex; i < endIndex; i++)
                {
                    message += $"<color={config.PlayerNameColor}>{onlinePlayers[i].displayName}</color>\n";
                }
                
                if (page < totalPages)
                {
                    message += $"\nType <color=#AAAAAA>/online {page + 1}</color> for next page";
                }
            }
            else
            {
                // Add each player to the message without pagination
                foreach (var onlinePlayer in onlinePlayers)
                {
                    message += $"<color={config.PlayerNameColor}>{onlinePlayer.displayName}</color>\n";
                }
            }
            
            // Add sleepers if admin
            if (isAdmin && config.ShowSleepersToAdminsOnly)
            {
                var sleepers = BasePlayer.sleepingPlayerList;
                
                if (sleepers.Count > 0)
                {
                    message += $"\n<color={config.SleepersHeaderColor}>Sleeping Players ({sleepers.Count}):</color>\n";
                    
                    foreach (var sleeper in sleepers)
                    {
                        message += $"<color={config.PlayerNameColor}>{sleeper.displayName}</color>\n";
                    }
                }
            }
            
            // Send the message to the player
            SendReply(player, message);
        }
        
        #endregion
    }
}
