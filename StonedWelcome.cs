using Oxide.Core.Plugins;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("StonedWelcome", "YourName", "1.0.0")]
    [Description("Displays a welcome message when a player joins.")]
    public class StonedWelcome : RustPlugin
    {
        void OnPlayerConnected(BasePlayer player)
        {
            timer.Once(5f, () =>
            {
                if (player != null && player.IsConnected)
                {
                    SendReply(player, "<color=#ffffff>Welcome to</color> <color=#d17fff>StonedFlorida420</color>!");
                    SendReply(player, "<color=#ffffff>Website:</color> <color=#7fafff>Stoned.gg</color>");
                    SendReply(player, "<color=#ffffff>Discord:</color> <color=#7fafff>discord.gg/StonedFlorida420</color>");
                    SendReply(player, "<color=#ffffff>Store:</color> <color=#7fafff>store.Stoned.gg</color>");
                }
            });
        }
    }
}
