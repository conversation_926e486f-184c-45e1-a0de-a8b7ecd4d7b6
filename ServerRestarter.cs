using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries;
using Oxide.Core.Plugins;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Oxide.Plugins
{
    [Info("Server Restarter", "YourName", "1.0.1")]
    [Description("Automatically restarts the server at configured times with Carbon/Oxide support")]
    class ServerRestarter : RustPlugin
    {
        #region Configuration
        
        private Configuration config;
        
        class Configuration
        {
            [JsonProperty("Restart Method (Options: 'oxide' or 'carbon')")]
            public string RestartMethod = "oxide";
            
            [JsonProperty("Enable Daily Restart")]
            public bool EnableDailyRestart = true;
            
            [JsonProperty("Daily Restart Time (24-hour format, HH:MM)")]
            public string DailyRestartTime = "06:00";
            
            [JsonProperty("Additional Restart Times (24-hour format, HH:MM)")]
            public List<string> AdditionalRestartTimes = new List<string> { "12:00", "18:00", "00:00" };
            
            [JsonProperty("Warning Messages (minutes before restart)")]
            public Dictionary<int, string> WarningMessages = new Dictionary<int, string>
            {
                [30] = "Server will restart in 30 minutes",
                [15] = "Server will restart in 15 minutes",
                [10] = "Server will restart in 10 minutes",
                [5] = "Server will restart in 5 minutes",
                [1] = "Server will restart in 1 minute"
            };
            
            [JsonProperty("Final Restart Message")]
            public string FinalRestartMessage = "Server is restarting now!";
            
            [JsonProperty("Enable Discord Webhook Notifications")]
            public bool EnableDiscordNotifications = false;
            
            [JsonProperty("Discord Webhook URL")]
            public string DiscordWebhookUrl = "";
        }
        
        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
                
                // Validate restart method
                if (config.RestartMethod != "oxide" && config.RestartMethod != "carbon")
                {
                    PrintWarning("Invalid restart method in config. Valid options are 'oxide' or 'carbon'. Defaulting to 'oxide'.");
                    config.RestartMethod = "oxide";
                }
            }
            catch
            {
                PrintWarning("Configuration file is corrupt, creating new configuration file");
                LoadDefaultConfig();
            }
            SaveConfig();
        }
        
        protected override void LoadDefaultConfig()
        {
            config = new Configuration();
        }
        
        protected override void SaveConfig()
        {
            Config.WriteObject(config, true);
        }
        
        #endregion
        
        #region Oxide Hooks
        
        private Timer restartCheckTimer;
        private Dictionary<int, Timer> warningTimers = new Dictionary<int, Timer>();
        
        void OnServerInitialized()
        {
            // Start checking for restart times every minute
            restartCheckTimer = timer.Every(60f, CheckRestartTimes);
            
            Puts("Server Restarter initialized!");
            Puts($"Using {config.RestartMethod.ToUpper()} for server restarts");
            
            // Combine all restart times for logging
            List<string> allRestartTimes = new List<string>();
            if (config.EnableDailyRestart)
                allRestartTimes.Add(config.DailyRestartTime);
            allRestartTimes.AddRange(config.AdditionalRestartTimes);
            
            Puts($"Configured restart times: {string.Join(", ", allRestartTimes)}");
        }
        
        void Unload()
        {
            // Clean up timers
            restartCheckTimer?.Destroy();
            foreach (var timer in warningTimers.Values)
                timer?.Destroy();
            warningTimers.Clear();
        }
        
        #endregion
        
        #region Core Functionality
        
        private void CheckRestartTimes()
        {
            DateTime now = DateTime.Now;
            string currentTime = now.ToString("HH:mm");
            
            // Get all active restart times
            List<string> allRestartTimes = new List<string>();
            if (config.EnableDailyRestart)
                allRestartTimes.Add(config.DailyRestartTime);
            allRestartTimes.AddRange(config.AdditionalRestartTimes);
            
            // Check if current time matches any restart time
            if (allRestartTimes.Contains(currentTime))
            {
                // Schedule the restart
                ScheduleRestart();
            }
            
            // Check for upcoming restarts to set warning timers
            foreach (string restartTime in allRestartTimes)
            {
                DateTime restartDateTime = ParseRestartTime(restartTime);
                
                // If restart time is in the future and within warning range
                if (restartDateTime > now)
                {
                    TimeSpan timeUntilRestart = restartDateTime - now;
                    
                    // Set warning timers if they're not already set
                    foreach (int warningMinute in config.WarningMessages.Keys)
                    {
                        if (timeUntilRestart.TotalMinutes <= warningMinute && 
                            timeUntilRestart.TotalMinutes > warningMinute - 1 && 
                            !warningTimers.ContainsKey(warningMinute))
                        {
                            float delaySeconds = (float)(timeUntilRestart.TotalMinutes - warningMinute) * 60f;
                            warningTimers[warningMinute] = timer.Once(delaySeconds, () => 
                            {
                                BroadcastWarning(config.WarningMessages[warningMinute]);
                                warningTimers.Remove(warningMinute);
                            });
                        }
                    }
                }
            }
        }
        
        private DateTime ParseRestartTime(string timeString)
        {
            string[] timeParts = timeString.Split(':');
            int hours = int.Parse(timeParts[0]);
            int minutes = int.Parse(timeParts[1]);
            
            DateTime now = DateTime.Now;
            DateTime restartTime = new DateTime(now.Year, now.Month, now.Day, hours, minutes, 0);
            
            // If the time has already passed today, set it for tomorrow
            if (restartTime < now)
                restartTime = restartTime.AddDays(1);
                
            return restartTime;
        }
        
        private void ScheduleRestart()
        {
            // Broadcast final message
            BroadcastWarning(config.FinalRestartMessage);
            
            // Send Discord notification if enabled
            if (config.EnableDiscordNotifications && !string.IsNullOrEmpty(config.DiscordWebhookUrl))
                SendDiscordNotification("Server is restarting now!");
            
            // Execute restart after a short delay to allow messages to be sent
            timer.Once(3f, () => 
            {
                if (config.RestartMethod.ToLower() == "carbon")
                    RestartWithCarbon();
                else
                    RestartWithOxide();
            });
        }
        
        private void RestartWithOxide()
        {
            Puts("Executing server restart using Oxide...");
            Server.Command("restart");
        }
        
        private void RestartWithCarbon()
        {
            Puts("Executing server restart using Carbon...");
            Server.Command("carbon.restart");
        }
        
        private void BroadcastWarning(string message)
        {
            Puts($"Broadcasting: {message}");
            Server.Broadcast($"<color=orange>{message}</color>");
            
            // Send to console as well
            foreach (var player in BasePlayer.activePlayerList)
            {
                if (player?.net?.connection != null)
                    player.ConsoleMessage(message);
            }
        }
        
        private void SendDiscordNotification(string message)
        {
            webrequest.Enqueue(config.DiscordWebhookUrl, 
                $"{{\"content\":\"{message}\"}}", 
                (code, response) => {}, 
                this, RequestMethod.POST, 
                new Dictionary<string, string> { ["Content-Type"] = "application/json" });
        }
        
        #endregion
        
        #region Commands
        
        [ChatCommand("nextrestart")]
        private void CmdNextRestart(BasePlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;
            
            DateTime now = DateTime.Now;
            DateTime? nextRestart = null;
            
            // Get all active restart times
            List<string> allRestartTimes = new List<string>();
            if (config.EnableDailyRestart)
                allRestartTimes.Add(config.DailyRestartTime);
            allRestartTimes.AddRange(config.AdditionalRestartTimes);
            
            foreach (string restartTime in allRestartTimes)
            {
                DateTime restartDateTime = ParseRestartTime(restartTime);
                
                if (nextRestart == null || restartDateTime < nextRestart)
                    nextRestart = restartDateTime;
            }
            
            if (nextRestart.HasValue)
            {
                TimeSpan timeUntilRestart = nextRestart.Value - now;
                player.ChatMessage($"Next restart: {nextRestart.Value.ToString("HH:mm")} " +
                                  $"({Math.Floor(timeUntilRestart.TotalHours)}h {timeUntilRestart.Minutes}m from now)");
            }
            else
            {
                player.ChatMessage("No restart times configured.");
            }
        }
        
        [ChatCommand("forcerestart")]
        private void CmdForceRestart(BasePlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;
            
            int delay = 0;
            if (args.Length > 0 && int.TryParse(args[0], out delay))
            {
                player.ChatMessage($"Server will restart in {delay} minutes");
                timer.Once(delay * 60f, ScheduleRestart);
            }
            else
            {
                player.ChatMessage("Restarting server now...");
                ScheduleRestart();
            }
        }
        
        #endregion
    }
}
