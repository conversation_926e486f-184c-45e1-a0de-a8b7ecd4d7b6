services:
  bot1:
    build:
      context: ./bot1
    restart: unless-stopped
    env_file:
      - ./bot1/.env
    volumes:
      - ./bot1:/app
      - bot1_node_modules:/app/node_modules

  bot2:
    build:
      context: ./bot2
    restart: unless-stopped
    env_file:
      - ./bot2/.env
    volumes:
      - ./bot2:/app
      - bot2_node_modules:/app/node_modules

  bot3:
    build:
      context: ./bot3
    restart: unless-stopped
    env_file:
      - ./bot3/.env
    volumes:
      - ./bot3:/app
      - bot3_node_modules:/app/node_modules

volumes:
  bot1_node_modules:
  bot2_node_modules:
  bot3_node_modules:
