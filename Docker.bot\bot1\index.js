const { Client, GatewayIntentBits } = require('discord.js');
const client = new Client({ 
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent
  ] 
});

// Add this near the top of your file
client.on('error', error => {
  console.error('Discord client error:', error);
});

// Add this to handle API errors
process.on('unhandledRejection', error => {
  console.error('Unhandled promise rejection:', error);
});

// Bot configuration
const BOT_NAME = 'Rust Server 1 Bot';
const SERVER_NAME = process.env.SERVER_NAME || 'Rust Server 1';

client.once('ready', () => {
  console.log(`${BOT_NAME} is online!`);
});

client.on('messageCreate', message => {
  if (message.author.bot) return;
  
  // Check if channel exists before processing commands
  if (!message.channel) {
    console.error(`Channel is undefined for server: ${message.guild?.name || 'Unknown'}`);
    return;
  }
  
  if (message.content.startsWith('!status')) {
    message.reply(`${SERVER_NAME} is running!`);
  }
});

client.login(process.env.DISCORD_TOKEN);

