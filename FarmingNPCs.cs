using Oxide.Core;
using Oxide.Core.Plugins;
using Oxide.Core.Libraries.Covalence;
using System.Collections.Generic;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("NPCFarmPlus", "YourName", "1.2.0")]
    [Description("NPCs (scientists) farm with you with inventories, UI, and persistence")]
    public class NPCFarmPlus : RustPlugin
    {
        #region Config

        private Dictionary<string, NPCType> npcTypes = new();
        private float followDistance = 3f;
        private float maxDistance = 20f;
        private int maxNPCsPerPlayer = 1;
        private float spawnCooldown = 60f;

        protected override void LoadDefaultConfig()
        {
            npcTypes = new Dictionary<string, NPCType>
            {
                { "scientist", new NPCType("assets/rust.ai/agents/npcplayer/humannpc/scientist/scientist.prefab", 0.5f, 200) },
                { "heavy", new NPCType("assets/rust.ai/agents/npcplayer/humannpc/scientist/scientistheavy.prefab", 0.3f, 300) }
            };
            Config["NPC_Types"] = npcTypes;
            Config["FollowDistance"] = followDistance = GetConfig("FollowDistance", 3f);
            Config["MaxDistance"] = maxDistance = GetConfig("MaxDistance", 20f);
            Config["MaxNPCsPerPlayer"] = maxNPCsPerPlayer = GetConfig("MaxNPCsPerPlayer", 1);
            Config["SpawnCooldown"] = spawnCooldown = GetConfig("SpawnCooldown", 60f);
            SaveConfig();
        }

        private T GetConfig<T>(string key, T defaultValue)
        {
            if (Config[key] == null) return defaultValue;
            return (T)System.Convert.ChangeType(Config[key], typeof(T));
        }

        #endregion

        #region Data

        private Dictionary<ulong, List<NPCFollower>> npcFollowers = new();
        private Dictionary<ulong, double> lastSpawnTime = new();
        private Dictionary<ulong, List<NPCSaveData>> savedNPCs = new();

        private class NPCFollower
        {
            public BaseNpc npc;
            public string type;
            public float gatherRate;
            public int inventory;
            public int capacity;
        }

        private class NPCType
        {
            public string prefab;
            public float gatherRate;
            public int capacity;

            public NPCType(string prefab, float rate, int cap)
            {
                this.prefab = prefab;
                this.gatherRate = rate;
                this.capacity = cap;
            }
        }

        private class NPCSaveData
        {
            public string prefab;
            public Vector3 position;
            public float gatherRate;
            public int inventory;
            public int capacity;
            public string type;
        }

        #endregion

        #region Commands

        [ChatCommand("npcfarm")]
        private void CmdSpawnNPC(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, "npcfarm.use"))
            {
                player.ChatMessage("You don't have permission to use this command.");
                return;
            }

            if (args.Length == 0 || !npcTypes.ContainsKey(args[0]))
            {
                player.ChatMessage("Usage: /npcfarm <scientist|heavy>");
                return;
            }

            if (!npcFollowers.ContainsKey(player.userID))
                npcFollowers[player.userID] = new List<NPCFollower>();

            if (npcFollowers[player.userID].Count >= maxNPCsPerPlayer)
            {
                player.ChatMessage("You already have the max number of NPCs.");
                return;
            }

            if (lastSpawnTime.ContainsKey(player.userID) && (Interface.Oxide.Now - lastSpawnTime[player.userID]) < spawnCooldown)
            {
                player.ChatMessage("You must wait before spawning another NPC.");
                return;
            }

            var npcData = npcTypes[args[0]];
            var npc = SpawnNPC(player.transform.position + (player.transform.forward * 2f), npcData.prefab);
            if (npc != null)
            {
                npcFollowers[player.userID].Add(new NPCFollower { npc = npc, type = args[0], gatherRate = npcData.gatherRate, inventory = 0, capacity = npcData.capacity });
                lastSpawnTime[player.userID] = Interface.Oxide.Now;
                player.ChatMessage($"{args[0].ToUpper()} spawned to farm with you.");
            }
        }

        [ChatCommand("npcdismiss")]
        private void CmdDismissNPC(BasePlayer player, string command, string[] args)
        {
            if (!npcFollowers.ContainsKey(player.userID)) return;

            foreach (var follower in npcFollowers[player.userID])
            {
                DropResources(player.transform.position, follower.inventory);
                follower.npc.Kill();
                player.ChatMessage($"Dismissed a {follower.type} NPC and dropped {follower.inventory} resources.");
            }
            npcFollowers[player.userID].Clear();
        }

        [ChatCommand("npcinv")]
        private void CmdShowInventory(BasePlayer player, string command, string[] args)
        {
            if (!npcFollowers.ContainsKey(player.userID))
            {
                player.ChatMessage("You have no NPCs.");
                return;
            }

            foreach (var follower in npcFollowers[player.userID])
            {
                player.ChatMessage($"{follower.type.ToUpper()} Inventory: {follower.inventory}/{follower.capacity}");
            }
        }

        #endregion

        #region Logic

        private void OnServerSave()
        {
            savedNPCs.Clear();
            foreach (var kv in npcFollowers)
            {
                var list = new List<NPCSaveData>();
                foreach (var f in kv.Value)
                {
                    list.Add(new NPCSaveData
                    {
                        prefab = npcTypes[f.type].prefab,
                        position = f.npc.transform.position,
                        gatherRate = f.gatherRate,
                        inventory = f.inventory,
                        capacity = f.capacity,
                        type = f.type
                    });
                }
                savedNPCs[kv.Key] = list;
            }
            Interface.Oxide.DataFileSystem.WriteObject("NPCFarmPlus_Saved", savedNPCs);
        }

        private void OnServerInitialized()
        {
            savedNPCs = Interface.Oxide.DataFileSystem.ReadObject<Dictionary<ulong, List<NPCSaveData>>>("NPCFarmPlus_Saved");

            foreach (var kv in savedNPCs)
            {
                var list = new List<NPCFollower>();
                foreach (var data in kv.Value)
                {
                    var npc = SpawnNPC(data.position, data.prefab);
                    if (npc != null)
                    {
                        list.Add(new NPCFollower { npc = npc, type = data.type, gatherRate = data.gatherRate, inventory = data.inventory, capacity = data.capacity });
                    }
                }
                npcFollowers[kv.Key] = list;
            }

            timer.Every(2f, () =>
            {
                foreach (var entry in npcFollowers)
                {
                    var player = BasePlayer.FindByID(entry.Key);
                    if (player == null || !player.IsAlive()) continue;

                    foreach (var follower in entry.Value.ToArray())
                    {
                        if (follower.npc == null || follower.npc.IsDestroyed)
                        {
                            entry.Value.Remove(follower);
                            continue;
                        }

                        float dist = Vector3.Distance(player.transform.position, follower.npc.transform.position);
                        if (dist > followDistance)
                            var apex = follower.npc as NPCPlayerApex;
							if (apex != null)
							{
								apex.SetDestination(player.transform.position);
							}
                        if (dist > maxDistance)
                        {
                            follower.npc.Kill();
                            entry.Value.Remove(follower);
                            player.ChatMessage("An NPC got lost and was removed.");
                            continue;
                        }

                        var hits = Physics.SphereCastAll(follower.npc.transform.position, 2f, Vector3.down, 1f, LayerMask.GetMask("Default"));
                        foreach (var hit in hits)
                        {
                            var resource = hit.collider.GetComponent<ResourceEntity>();
                            if (resource != null && follower.inventory < follower.capacity)
                            {
                                float damage = 10f * follower.gatherRate;
                                resource.health -= damage;
                                if (resource.health <= 0f)
                                {
                                    resource.Kill();
                                    follower.inventory += 10;
                                    player.ChatMessage($"Your {follower.type} gathered 10 units. Total: {follower.inventory}/{follower.capacity}");
                                }
                                break;
                            }
                        }
                    }
                }
            });
        }

        private BaseNpc SpawnNPC(Vector3 position, string prefab)
        {
            BaseEntity entity = GameManager.server.CreateEntity(prefab, position);
            if (entity == null) return null;
            entity.Spawn();
            return entity as BaseNpc;
        }

        private void DropResources(Vector3 position, int amount)
        {
            Item item = ItemManager.CreateByName("wood", amount);
            if (item != null)
            {
                item.Drop(position, Vector3.down);
            }
        }

        #endregion

        #region Permissions

        private void Init()
        {
            permission.RegisterPermission("npcfarm.use", this);
        }

        #endregion
    }
}
 