using System;
using System.Collections.Generic;
using System.Linq; // Add this for Select extension method
using Oxide.Core;
using Oxide.Core.Plugins;
using UnityEngine;
using Newtonsoft.Json;
using Oxide.Game.Rust.Cui; // Add this for CUI support

namespace Oxide.Plugins
{
    [Info("Absolute PVE", "Frizzo420", "1.0.0")]
    [Description("Makes the server PVE by preventing player-to-player damage with extensive configuration options")]
    class AbsolutePve : RustPlugin
    {
        #region Configuration
        
        private Configuration config;
        
        class Configuration
        {
            [JsonProperty("General Settings")]
            public GeneralSettings General = new GeneralSettings();
            
            [JsonProperty("PVP Zones")]
            public ZoneSettings Zones = new ZoneSettings();
            
            [JsonProperty("NPC Damage Settings")]
            public NPCDamageSettings NPCDamage = new NPCDamageSettings();
            
            [JsonProperty("Notification Settings")]
            public NotificationSettings Notifications = new NotificationSettings();
            
            [JsonProperty("Schedule Settings")]
            public ScheduleSettings Schedule = new ScheduleSettings();
            
            [JsonProperty("Raid Settings")]
            public RaidSettings Raid = new RaidSettings();
            
            public class GeneralSettings
            {
                [JsonProperty("Allow damage to sleeping players")]
                public bool AllowDamageToSleepers = false;
                
                [JsonProperty("Allow damage to teammates")]
                public bool AllowDamageToTeammates = false;
                
                [JsonProperty("Allow damage to clan members")]
                public bool AllowDamageToClanMembers = false;
                
                [JsonProperty("Allow damage to friends")]
                public bool AllowDamageToFriends = false;
                
                [JsonProperty("Allow damage with melee weapons only")]
                public bool AllowMeleeDamageOnly = false;
                
                [JsonProperty("Allow damage with specific weapons (item shortnames)")]
                public List<string> AllowedWeapons = new List<string>();
                
                [JsonProperty("Block all damage types except these (bleeding, slash, etc)")]
                public List<string> AllowedDamageTypes = new List<string>();
            }
            
            public class ZoneSettings
            {
                [JsonProperty("Allow damage in monuments")]
                public bool AllowDamageInMonuments = false;
                
                [JsonProperty("Allow damage in specific PVP zones")]
                public bool AllowDamageInPvpZones = false;
                
                [JsonProperty("PVP zone names (lowercase)")]
                public List<string> PvpZones = new List<string> { 
                    "launch site", 
                    "military tunnel", 
                    "oil rig", 
                    "large oil rig", 
                    "cargo ship" 
                };
                
                [JsonProperty("PVP radius around monuments (meters)")]
                public float PvpRadiusAroundMonuments = 0f;
                
                [JsonProperty("Custom PVP zones (x,y,z,radius)")]
                public List<string> CustomPvpZones = new List<string>();
            }
            
            public class NPCDamageSettings
            {
                [JsonProperty("Block trap damage to players")]
                public bool BlockTrapDamage = false;
                
                [JsonProperty("Block helicopter damage to players")]
                public bool BlockHeliDamage = false;
                
                [JsonProperty("Block Bradley APC damage to players")]
                public bool BlockBradleyDamage = false;
                
                [JsonProperty("Block turret damage to players")]
                public bool BlockTurretDamage = false;
                
                [JsonProperty("Block scientist NPC damage to players")]
                public bool BlockScientistDamage = false;
                
                [JsonProperty("Block animal damage to players")]
                public bool BlockAnimalDamage = false;
            }
            
            public class NotificationSettings
            {
                [JsonProperty("Enable PVP attempt notifications")]
                public bool EnableNotifications = true;
                
                [JsonProperty("PVE message")]
                public string PveMessage = "PVP is disabled on this server!";
                
                [JsonProperty("PVP zone entry message")]
                public string PvpZoneEntryMessage = "You have entered a PVP zone!";
                
                [JsonProperty("PVP zone exit message")]
                public string PvpZoneExitMessage = "You have left a PVP zone!";
                
                [JsonProperty("Use UI notifications instead of chat")]
                public bool UseUINotifications = false;
                
                [JsonProperty("UI notification color (hex)")]
                public string UIColor = "#FF0000";
                
                [JsonProperty("UI notification duration (seconds)")]
                public float UIDuration = 5f;
            }
            
            public class ScheduleSettings
            {
                [JsonProperty("Enable scheduled PVP times")]
                public bool EnableScheduledPvp = false;
                
                [JsonProperty("PVP days (0=Sunday, 1=Monday, etc)")]
                public List<int> PvpDays = new List<int> { 5, 6 }; // Friday and Saturday
                
                [JsonProperty("PVP start hour (24-hour format)")]
                public int PvpStartHour = 18; // 6 PM
                
                [JsonProperty("PVP end hour (24-hour format)")]
                public int PvpEndHour = 22; // 10 PM
                
                [JsonProperty("Scheduled PVP message")]
                public string ScheduledPvpMessage = "PVP is now enabled for the next {0} hours!";
                
                [JsonProperty("Scheduled PVE message")]
                public string ScheduledPveMessage = "PVP is now disabled!";
            }
            
            public class RaidSettings
            {
                [JsonProperty("Enable PVP during raids")]
                public bool EnablePvpDuringRaids = false;
                
                [JsonProperty("PVP radius around raid (meters)")]
                public float PvpRadiusAroundRaid = 100f;
                
                [JsonProperty("PVP duration after raid starts (minutes)")]
                public float PvpDurationAfterRaid = 30f;
                
                [JsonProperty("Raid triggers (explosion, etc)")]
                public List<string> RaidTriggers = new List<string> { 
                    "rocket", "c4", "satchel", "explosive.timed" 
                };
                
                [JsonProperty("Raid PVP enabled message")]
                public string RaidPvpEnabledMessage = "PVP is enabled around the raid for {0} minutes!";
                
                [JsonProperty("Raid PVP disabled message")]
                public string RaidPvpDisabledMessage = "PVP is now disabled around the raid area.";
            }
        }
        
        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
                
                // Clean up duplicate entries in lists
                CleanupConfigLists();
            }
            catch
            {
                PrintWarning("Configuration file is corrupt, creating new configuration file!");
                LoadDefaultConfig();
            }
            SaveConfig();
        }
        
        protected override void LoadDefaultConfig()
        {
            config = new Configuration();
            PrintWarning("Default configuration file created!");
        }
        
        protected override void SaveConfig()
        {
            // Clean up duplicate entries in lists before saving
            CleanupConfigLists();
            Config.WriteObject(config);
        }

        private void CleanupConfigLists()
        {
            // Clean up PVP zone names
            if (config.Zones.PvpZones != null && config.Zones.PvpZones.Count > 0)
            {
                config.Zones.PvpZones = config.Zones.PvpZones.Distinct().ToList();
            }
            
            // Clean up PVP days
            if (config.Schedule.PvpDays != null && config.Schedule.PvpDays.Count > 0)
            {
                config.Schedule.PvpDays = config.Schedule.PvpDays.Distinct().ToList();
            }
            
            // Clean up Raid triggers
            if (config.Raid.RaidTriggers != null && config.Raid.RaidTriggers.Count > 0)
            {
                config.Raid.RaidTriggers = config.Raid.RaidTriggers.Distinct().ToList();
            }
            
            // Clean up Custom PVP zones
            if (config.Zones.CustomPvpZones != null && config.Zones.CustomPvpZones.Count > 0)
            {
                config.Zones.CustomPvpZones = config.Zones.CustomPvpZones.Distinct().ToList();
            }
            
            // Clean up Allowed weapons
            if (config.General.AllowedWeapons != null && config.General.AllowedWeapons.Count > 0)
            {
                config.General.AllowedWeapons = config.General.AllowedWeapons.Distinct().ToList();
            }
            
            // Clean up Allowed damage types
            if (config.General.AllowedDamageTypes != null && config.General.AllowedDamageTypes.Count > 0)
            {
                config.General.AllowedDamageTypes = config.General.AllowedDamageTypes.Distinct().ToList();
            }
        }
        
        #endregion
        
        #region Data Storage
        
        private Dictionary<ulong, Timer> playerPvpTimers = new Dictionary<ulong, Timer>();
        private Dictionary<Vector3, Timer> raidPvpZones = new Dictionary<Vector3, Timer>();
        private List<CustomPvpZone> customPvpZones = new List<CustomPvpZone>();
        // Removed: private string webhookUrl = ""; // Store the webhook URL
        
        private class CustomPvpZone
        {
            public Vector3 Position { get; set; }
            public float Radius { get; set; }
        }
        
        #endregion
        
        #region Hooks
        
        void OnServerInitialized()
        {
            // Register permissions
            permission.RegisterPermission("absolutepve.admin", this);
            permission.RegisterPermission("absolutepve.bypass", this);
            
            // Parse custom PVP zones
            ParseCustomPvpZones();
            
            // Start scheduled PVP timer if enabled
            if (config.Schedule.EnableScheduledPvp)
                timer.Every(60f, CheckScheduledPvp);
            
            // Get monument list
            GetMonumentList();
            
            Puts("AbsolutePve plugin initialized!");
        }
        
        void Unload()
        {
            // Clean up timers
            foreach (var timer in playerPvpTimers.Values)
                timer?.Destroy();
                
            foreach (var timer in raidPvpZones.Values)
                timer?.Destroy();
        }
        
        object OnEntityTakeDamage(BaseCombatEntity entity, HitInfo info)
        {
            // If entity or info is null, allow damage
            if (entity == null || info == null || info.Initiator == null)
                return null;
                
            // If entity is not a player, allow damage
            var victim = entity as BasePlayer;
            if (victim == null)
                return null;
                
            // If damage is not from a player, handle NPC damage
            var attacker = info.Initiator.ToPlayer();
            if (attacker == null)
                return HandleNpcDamage(victim, info);
                
            // If attacker is the same as victim, allow damage (self-harm)
            if (attacker == victim)
                return null;
                
            // Check if attacker has bypass permission
            if (permission.UserHasPermission(attacker.UserIDString, "absolutepve.bypass"))
                return null;
                
            // Check if PVP is allowed based on various conditions
            if (IsPvpAllowed(attacker, victim, info))
                return null;
                
            // If we got here, block the damage
            if (config.Notifications.EnableNotifications)
                SendPvpNotification(attacker, config.Notifications.PveMessage);
                
            return true; // Block damage
        }
        
        void OnExplosiveThrown(BasePlayer player, BaseEntity entity)
        {
            if (!config.Raid.EnablePvpDuringRaids) return;
            
            // Check if the entity is a raid trigger
            string entityName = entity?.ShortPrefabName ?? "";
            if (config.Raid.RaidTriggers.Contains(entityName))
            {
                // Create a temporary PVP zone around the raid
                CreateRaidPvpZone(entity.transform.position);
            }
        }
        
        void OnExplosiveDropped(BasePlayer player, BaseEntity entity)
        {
            if (!config.Raid.EnablePvpDuringRaids) return;
            
            // Check if the entity is a raid trigger
            string entityName = entity?.ShortPrefabName ?? "";
            if (config.Raid.RaidTriggers.Contains(entityName))
            {
                // Create a temporary PVP zone around the raid
                CreateRaidPvpZone(entity.transform.position);
            }
        }
        
        #endregion
        
        #region Helper Methods
        
        private void ParseCustomPvpZones()
        {
            customPvpZones.Clear();
            
            foreach (var zoneString in config.Zones.CustomPvpZones)
            {
                try
                {
                    string[] parts = zoneString.Split(',');
                    if (parts.Length != 4) continue;
                    
                    float x = float.Parse(parts[0]);
                    float y = float.Parse(parts[1]);
                    float z = float.Parse(parts[2]);
                    float radius = float.Parse(parts[3]);
                    
                    customPvpZones.Add(new CustomPvpZone
                    {
                        Position = new Vector3(x, y, z),
                        Radius = radius
                    });
                }
                catch (Exception ex)
                {
                    PrintError($"Error parsing custom PVP zone: {zoneString} - {ex.Message}");
                }
            }
            
            Puts($"Loaded {customPvpZones.Count} custom PVP zones");
        }
        
        private void CreateRaidPvpZone(Vector3 position)
        {
            // Create a temporary PVP zone around the raid
            if (raidPvpZones.ContainsKey(position)) return;
            
            // Broadcast to nearby players
            List<BasePlayer> nearbyPlayers = new List<BasePlayer>();
            Vis.Entities(position, config.Raid.PvpRadiusAroundRaid, nearbyPlayers);
            
            foreach (var player in nearbyPlayers)
            {
                SendPvpNotification(player, string.Format(config.Raid.RaidPvpEnabledMessage, 
                    config.Raid.PvpDurationAfterRaid));
            }
            
            // Create a timer to remove the zone after the duration
            Timer zoneTimer = timer.Once(config.Raid.PvpDurationAfterRaid * 60f, () => {
                if (raidPvpZones.ContainsKey(position))
                {
                    raidPvpZones.Remove(position);
                    
                    // Notify nearby players that PVP is disabled
                    List<BasePlayer> playersNearby = new List<BasePlayer>();
                    Vis.Entities(position, config.Raid.PvpRadiusAroundRaid, playersNearby);
                    
                    foreach (var player in playersNearby)
                    {
                        SendPvpNotification(player, config.Raid.RaidPvpDisabledMessage);
                    }
                }
            });
            
            raidPvpZones[position] = zoneTimer;
        }
        
        private void CheckScheduledPvp()
        {
            bool isPvpTime = IsScheduledPvpTime();
            
            // TODO: Implement scheduled PVP time announcements
            // This would broadcast to all players when PVP time starts/ends
        }
        
        private void SendPvpNotification(BasePlayer player, string message)
        {
            if (player == null || string.IsNullOrEmpty(message)) return;
            
            if (config.Notifications.UseUINotifications)
            {
                // Send UI notification
                string color = config.Notifications.UIColor;
                float duration = config.Notifications.UIDuration;
                
                // Example UI notification using a simpler approach
                player.SendConsoleCommand("ui.create", "pvpnotification", "ui.alert", 
                    $"{{\"text\":\"{message}\",\"textColor\":\"{color}\",\"duration\":{duration}}}");
            }
            else
            {
                // Send chat notification
                SendReply(player, message);
            }
        }
        
        private object HandleNpcDamage(BasePlayer victim, HitInfo info)
        {
            var entity = info.Initiator;
            
            // Check for trap damage
            if (config.NPCDamage.BlockTrapDamage && IsTrap(entity))
                return true;
                
            // Check for helicopter damage
            if (config.NPCDamage.BlockHeliDamage && IsHelicopter(entity))
                return true;
                
            // Check for Bradley APC damage
            if (config.NPCDamage.BlockBradleyDamage && IsBradley(entity))
                return true;
                
            // Check for turret damage
            if (config.NPCDamage.BlockTurretDamage && IsTurret(entity))
                return true;
                
            // Check for scientist damage
            if (config.NPCDamage.BlockScientistDamage && IsScientist(entity))
                return true;
                
            // Check for animal damage
            if (config.NPCDamage.BlockAnimalDamage && IsAnimal(entity))
                return true;
                
            return null;
        }
        
        private bool IsPvpAllowed(BasePlayer attacker, BasePlayer victim, HitInfo info)
        {
            // Check if victim is sleeping and config allows damage to sleepers
            if (victim.IsSleeping() && config.General.AllowDamageToSleepers)
                return true;
                
            // Check if attacker and victim are teammates and config allows team damage
            if (config.General.AllowDamageToTeammates && AreTeammates(attacker, victim))
                return true;
                
            // Check if attacker and victim are clan members and config allows clan damage
            if (config.General.AllowDamageToClanMembers && AreClanMembers(attacker, victim))
                return true;
                
            // Check if attacker and victim are friends and config allows friend damage
            if (config.General.AllowDamageToFriends && AreFriends(attacker, victim))
                return true;
                
            // Check if in a monument that allows PVP
            if (config.Zones.AllowDamageInMonuments && IsInMonument(victim.transform.position))
                return true;
                
            // Check if in a PVP zone
            if (config.Zones.AllowDamageInPvpZones && IsInPvpZone(victim.transform.position))
                return true;
                
            // Check if in a custom PVP zone
            if (config.Zones.AllowDamageInPvpZones && IsInCustomPvpZone(victim.transform.position))
                return true;
                
            // Check if in a raid PVP zone
            if (config.Raid.EnablePvpDuringRaids && IsInRaidPvpZone(victim.transform.position))
                return true;
                
            // Check if during scheduled PVP time
            if (config.Schedule.EnableScheduledPvp && IsScheduledPvpTime())
                return true;
                
            // Check if weapon is allowed
            if (config.General.AllowMeleeDamageOnly && IsMeleeWeapon(info))
                return true;
                
            // Check if specific weapon is allowed
            if (IsAllowedWeapon(info))
                return true;
                
            // Check if damage type is allowed
            if (IsAllowedDamageType(info))
                return true;
                
            return false;
        }
        
        private bool AreTeammates(BasePlayer player1, BasePlayer player2)
        {
            if (player1.currentTeam == 0UL || player2.currentTeam == 0UL)
                return false;
                
            return player1.currentTeam == player2.currentTeam;
        }
        
        private bool AreClanMembers(BasePlayer player1, BasePlayer player2)
        {
            // This would require Clans plugin integration
            // For now, return false or implement your own check
            return false;
        }
        
        private bool AreFriends(BasePlayer player1, BasePlayer player2)
        {
            // This would require Friends plugin integration
            // For now, return false or implement your own check
            return false;
        }
        
        private bool IsInMonument(Vector3 position)
        {
            foreach (var monument in TerrainMeta.Path.Monuments)
            {
                if (monument.IsInBounds(position))
                    return true;
                    
                // Check for PVP radius around monuments
                if (config.Zones.PvpRadiusAroundMonuments > 0)
                {
                    float distance = Vector3.Distance(position, monument.transform.position);
                    if (distance <= config.Zones.PvpRadiusAroundMonuments)
                        return true;
                }
            }
            return false;
        }
        
        private bool IsInPvpZone(Vector3 position)
        {
            foreach (var monument in TerrainMeta.Path.Monuments)
            {
                if (monument.IsInBounds(position))
                {
                    string monumentName = monument.name.ToLower();
                    foreach (var pvpZone in config.Zones.PvpZones)
                    {
                        if (monumentName.Contains(pvpZone))
                            return true;
                    }
                }
            }
            return false;
        }
        
        private bool IsInCustomPvpZone(Vector3 position)
        {
            foreach (var zone in customPvpZones)
            {
                float distance = Vector3.Distance(position, zone.Position);
                if (distance <= zone.Radius)
                    return true;
            }
            return false;
        }
        
        private bool IsInRaidPvpZone(Vector3 position)
        {
            foreach (var raidPos in raidPvpZones.Keys)
            {
                float distance = Vector3.Distance(position, raidPos);
                if (distance <= config.Raid.PvpRadiusAroundRaid)
                    return true;
            }
            return false;
        }
        
        private bool IsScheduledPvpTime()
        {
            DateTime now = DateTime.Now;
            int dayOfWeek = (int)now.DayOfWeek;
            int hour = now.Hour;
            
            // Check if today is a PVP day
            if (!config.Schedule.PvpDays.Contains(dayOfWeek))
                return false;
                
            // Check if current hour is within PVP hours
            if (config.Schedule.PvpStartHour < config.Schedule.PvpEndHour)
            {
                // Simple case: start and end on same day
                return hour >= config.Schedule.PvpStartHour && hour < config.Schedule.PvpEndHour;
            }
            else
            {
                // Complex case: PVP spans midnight
                return hour >= config.Schedule.PvpStartHour || hour < config.Schedule.PvpEndHour;
            }
        }
        
        private bool IsMeleeWeapon(HitInfo info)
        {
            if (info?.WeaponPrefab == null) return false;
            
            string weaponName = info.WeaponPrefab.ShortPrefabName;
            return weaponName.Contains("knife") || 
                   weaponName.Contains("sword") || 
                   weaponName.Contains("machete") || 
                   weaponName.Contains("salvaged") || 
                   weaponName.Contains("bone") || 
                   weaponName.Contains("mace") || 
                   weaponName.Contains("hammer") || 
                   weaponName.Contains("rock") || 
                   weaponName.Contains("torch") || 
                   weaponName.Contains("paddle") || 
                   weaponName.Contains("axe") || 
                   weaponName.Contains("pickaxe") || 
                   weaponName.Contains("hatchet") || 
                   weaponName.Contains("icepick");
        }
        
        private bool IsAllowedWeapon(HitInfo info)
        {
            if (info?.WeaponPrefab == null || config.General.AllowedWeapons.Count == 0) 
                return false;
                
            string weaponName = info.WeaponPrefab.ShortPrefabName;
            return config.General.AllowedWeapons.Contains(weaponName);
        }
        
        private bool IsAllowedDamageType(HitInfo info)
        {
            if (info == null || config.General.AllowedDamageTypes.Count == 0) 
                return false;
                
            string damageType = info.damageTypes.GetMajorityDamageType().ToString().ToLower();
            return config.General.AllowedDamageTypes.Contains(damageType);
        }
        
        private bool IsTrap(BaseEntity entity)
        {
            if (entity == null) return false;
            
            string prefabName = entity.ShortPrefabName ?? "";
            return prefabName.Contains("trap") || 
                   prefabName.Contains("landmine") || 
                   prefabName.Contains("beartrap");
        }
        
        private bool IsHelicopter(BaseEntity entity)
        {
            if (entity == null) return false;
            
            string prefabName = entity.ShortPrefabName ?? "";
            return prefabName.Contains("heli");
        }
        
        private bool IsBradley(BaseEntity entity)
        {
            if (entity == null) return false;
            
            string prefabName = entity.ShortPrefabName ?? "";
            return prefabName.Contains("bradley");
        }
        
        private bool IsTurret(BaseEntity entity)
        {
            if (entity == null) return false;
            
            string prefabName = entity.ShortPrefabName ?? "";
            return prefabName.Contains("turret");
        }
        
        private bool IsScientist(BaseEntity entity)
        {
            if (entity == null) return false;
            
            string prefabName = entity.ShortPrefabName ?? "";
            return prefabName.Contains("scientist") || 
                   prefabName.Contains("tunneldweller") || 
                   prefabName.Contains("bandit");
        }
        
        private bool IsAnimal(BaseEntity entity)
        {
            if (entity == null) return false;
            
            string prefabName = entity.ShortPrefabName ?? "";
            return prefabName.Contains("bear") || 
                   prefabName.Contains("wolf") || 
                   prefabName.Contains("boar") || 
                   prefabName.Contains("stag") || 
                   prefabName.Contains("chicken");
        }
        
        private void GetMonumentList()
        {
            // Get all monuments on the map
            List<string> monumentNames = new List<string>();
            foreach (var monument in TerrainMeta.Path.Monuments)
            {
                if (monument != null)
                {
                    monumentNames.Add(monument.name.ToLower());
                }
            }
            
            // Log the monument list
            Puts($"Found {monumentNames.Count} monuments on the map:");
            foreach (var name in monumentNames)
            {
                Puts($"- {name}");
            }
            
            // Removed webhook-related code here
        }
        
        // Removed SendMonumentListToWebhook method
        
        #endregion
        
        #region Chat Commands
        
        [ChatCommand("pve")]
        void PveCommand(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, "absolutepve.admin"))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            if (args.Length == 0)
            {
                SendReply(player, "AbsolutePVE is active. Use /pve reload to reload the config.");
                return;
            }
            
            string subCommand = args[0].ToLower();
            
            if (subCommand == "reload")
            {
                LoadConfig();
                SendReply(player, "AbsolutePVE configuration reloaded!");
            }
            else if (subCommand == "monuments")
            {
                // List all monuments on the server
                List<string> monumentNames = new List<string>();
                foreach (var monument in TerrainMeta.Path.Monuments)
                {
                    if (monument != null)
                    {
                        monumentNames.Add(monument.name.ToLower());
                    }
                }
                
                SendReply(player, $"Found {monumentNames.Count} monuments on the map:");
                foreach (var name in monumentNames)
                {
                    SendReply(player, $"- {name}");
                }
            }
            // Removed this entire else-if block:
            /*
            else if (subCommand == "webhook" && args.Length > 1)
            {
                // Set webhook URL
                webhookUrl = args[1];
                SendReply(player, $"Webhook URL set to: {webhookUrl}");
                
                // Save webhook URL to config
                config.WebhookUrl = webhookUrl;
                SaveConfig();
                
                // Test the webhook with monument list
                GetMonumentList();
            }
            */
        }
        
        [ChatCommand("pveui")]
        void PveUICommand(BasePlayer player, string command, string[] args)
        {
            // Check if player has admin permission
            if (!permission.UserHasPermission(player.UserIDString, "absolutepve.admin"))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            if (args.Length == 0)
            {
                // Open the main UI
                CreateConfigUI(player);
                return;
            }
            
            string subCommand = args[0].ToLower();
            
            if (subCommand == "close")
            {
                // Close the UI
                DestroyUI(player);
            }
            else if (subCommand == "save")
            {
                // Save configuration
                SaveConfig();
                SendReply(player, "Configuration saved!");
            }
            else if (subCommand == "tab" && args.Length > 1)
            {
                // Switch to a specific tab
                string tab = args[1].ToLower();
                switch (tab)
                {
                    case "general":
                        ShowGeneralTab(player);
                        break;
                    case "zones":
                        ShowZonesTab(player);
                        break;
                    case "npcs":
                        ShowNPCTab(player);
                        break;
                    case "notifications":
                        ShowNotificationsTab(player);
                        break;
                    case "schedule":
                        ShowScheduleTab(player);
                        break;
                    case "raid":
                        ShowRaidTab(player);
                        break;
                }
            }
            else if (subCommand == "toggle" && args.Length > 1)
            {
                // Toggle a boolean setting
                string setting = args[1].ToLower();
                
                if (setting == "general.sleepers")
                    config.General.AllowDamageToSleepers = !config.General.AllowDamageToSleepers;
                else if (setting == "general.teammates")
                    config.General.AllowDamageToTeammates = !config.General.AllowDamageToTeammates;
                else if (setting == "general.clanmembers")
                    config.General.AllowDamageToClanMembers = !config.General.AllowDamageToClanMembers;
                else if (setting == "general.friends")
                    config.General.AllowDamageToFriends = !config.General.AllowDamageToFriends;
                else if (setting == "general.melee")
                    config.General.AllowMeleeDamageOnly = !config.General.AllowMeleeDamageOnly;
                else if (setting == "zones.monuments")
                    config.Zones.AllowDamageInMonuments = !config.Zones.AllowDamageInMonuments;
                else if (setting == "zones.pvpzones")
                    config.Zones.AllowDamageInPvpZones = !config.Zones.AllowDamageInPvpZones;
                else if (setting == "npc.trap")
                    config.NPCDamage.BlockTrapDamage = !config.NPCDamage.BlockTrapDamage;
                else if (setting == "npc.heli")
                    config.NPCDamage.BlockHeliDamage = !config.NPCDamage.BlockHeliDamage;
                else if (setting == "npc.bradley")
                    config.NPCDamage.BlockBradleyDamage = !config.NPCDamage.BlockBradleyDamage;
                else if (setting == "npc.turret")
                    config.NPCDamage.BlockTurretDamage = !config.NPCDamage.BlockTurretDamage;
                else if (setting == "npc.scientist")
                    config.NPCDamage.BlockScientistDamage = !config.NPCDamage.BlockScientistDamage;
                else if (setting == "npc.animal")
                    config.NPCDamage.BlockAnimalDamage = !config.NPCDamage.BlockAnimalDamage;
                else if (setting == "notifications.enable")
                    config.Notifications.EnableNotifications = !config.Notifications.EnableNotifications;
                else if (setting == "notifications.ui")
                    config.Notifications.UseUINotifications = !config.Notifications.UseUINotifications;
                else if (setting == "schedule.enable")
                    config.Schedule.EnableScheduledPvp = !config.Schedule.EnableScheduledPvp;
                else if (setting == "raid.enable")
                    config.Raid.EnablePvpDuringRaids = !config.Raid.EnablePvpDuringRaids;
                
                // Refresh the current tab
                if (setting.StartsWith("general."))
                    ShowGeneralTab(player);
                else if (setting.StartsWith("zones."))
                    ShowZonesTab(player);
                else if (setting.StartsWith("npc."))
                    ShowNPCTab(player);
                else if (setting.StartsWith("notifications."))
                    ShowNotificationsTab(player);
                else if (setting.StartsWith("schedule."))
                    ShowScheduleTab(player);
                else if (setting.StartsWith("raid."))
                    ShowRaidTab(player);
            }
            else if (subCommand == "slider" && args.Length > 2)
            {
                // Update a slider value
                string setting = args[1].ToLower();
                float value;
                
                if (!float.TryParse(args[2], out value))
                    return;
                
                if (setting == "zones.radius")
                    config.Zones.PvpRadiusAroundMonuments = value;
                else if (setting == "notifications.duration")
                    config.Notifications.UIDuration = value;
                else if (setting == "schedule.starthour")
                    config.Schedule.PvpStartHour = (int)value;
                else if (setting == "schedule.endhour")
                    config.Schedule.PvpEndHour = (int)value;
                else if (setting == "raid.radius")
                    config.Raid.PvpRadiusAroundRaid = value;
                else if (setting == "raid.duration")
                    config.Raid.PvpDurationAfterRaid = value;
                
                // Refresh the current tab
                if (setting.StartsWith("zones."))
                    ShowZonesTab(player);
                else if (setting.StartsWith("notifications."))
                    ShowNotificationsTab(player);
                else if (setting.StartsWith("schedule."))
                    ShowScheduleTab(player);
                else if (setting.StartsWith("raid."))
                    ShowRaidTab(player);
            }
        }
        
        #endregion

        #region UI System

        private const string UIMain = "AbsolutePVE_UI";

        private void CreateConfigUI(BasePlayer player)
        {
            if (player == null) return;
            
            // Destroy any existing UI
            CuiHelper.DestroyUi(player, UIMain);
            
            var container = new CuiElementContainer();
            
            // Main panel
            container.Add(new CuiPanel
            {
                CursorEnabled = true,
                RectTransform = { AnchorMin = "0.2 0.2", AnchorMax = "0.8 0.8" },
                Image = { Color = "0.1 0.1 0.1 0.95" }
            }, "Overlay", UIMain);
            
            // Title
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = "0.1 0.9", AnchorMax = "0.9 0.95" },
                Text = { Text = "AbsolutePVE Configuration", FontSize = 20, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, UIMain);
            
            // Close button
            container.Add(new CuiButton
            {
                RectTransform = { AnchorMin = "0.85 0.9", AnchorMax = "0.95 0.95" },
                Button = { Color = "0.7 0.2 0.2 0.9", Command = "pve.closeui" },
                Text = { Text = "X", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, UIMain);
            
            // Save button
            container.Add(new CuiButton
            {
                RectTransform = { AnchorMin = "0.35 0.05", AnchorMax = "0.65 0.1" },
                Button = { Color = "0.2 0.7 0.2 0.9", Command = "pve.saveconfig" },
                Text = { Text = "Save Configuration", FontSize = 14, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, UIMain);
            
            // Settings
            float yPos = 0.85f;
            float yOffset = 0.05f;
            
            // General settings
            AddSectionTitle(container, "General Settings", yPos);
            yPos -= yOffset;
            
            AddToggle(container, "Allow damage to sleeping players", config.General.AllowDamageToSleepers, "pve.toggle general.sleepers", yPos);
            yPos -= yOffset;
            
            AddToggle(container, "Allow damage to teammates", config.General.AllowDamageToTeammates, "pve.toggle general.teammates", yPos);
            yPos -= yOffset;
            
            AddToggle(container, "Allow damage to clan members", config.General.AllowDamageToClanMembers, "pve.toggle general.clanmembers", yPos);
            yPos -= yOffset;
            
            AddToggle(container, "Allow damage to friends", config.General.AllowDamageToFriends, "pve.toggle general.friends", yPos);
            yPos -= yOffset;
            
            AddToggle(container, "Allow melee damage only", config.General.AllowMeleeDamageOnly, "pve.toggle general.melee", yPos);
            yPos -= yOffset;
            
            // Zone settings
            AddSectionTitle(container, "Zone Settings", yPos);
            yPos -= yOffset;
            
            AddToggle(container, "Allow damage in monuments", config.Zones.AllowDamageInMonuments, "pve.toggle zones.monuments", yPos);
            yPos -= yOffset;
            
            AddToggle(container, "Allow damage in PVP zones", config.Zones.AllowDamageInPvpZones, "pve.toggle zones.pvpzones", yPos);
            yPos -= yOffset;
            
            // NPC settings
            AddSectionTitle(container, "NPC Settings", yPos);
            yPos -= yOffset;
            
            AddToggle(container, "Block trap damage", config.NPCDamage.BlockTrapDamage, "pve.toggle npc.trap", yPos);
            yPos -= yOffset;
            
            AddToggle(container, "Block helicopter damage", config.NPCDamage.BlockHeliDamage, "pve.toggle npc.heli", yPos);
            yPos -= yOffset;
            
            AddToggle(container, "Block Bradley damage", config.NPCDamage.BlockBradleyDamage, "pve.toggle npc.bradley", yPos);
            yPos -= yOffset;
            
            // Send UI to player
            CuiHelper.AddUi(player, container);
        }

        private void AddSectionTitle(CuiElementContainer container, string title, float yPos)
        {
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = $"0.1 {yPos}", AnchorMax = $"0.9 {yPos + 0.05f}" },
                Text = { Text = title, FontSize = 16, Align = TextAnchor.MiddleLeft, Color = "0.9 0.9 0.5 1" }
            }, UIMain);
        }

        private void AddToggle(CuiElementContainer container, string label, bool value, string command, float yPos)
        {
            container.Add(new CuiButton
            {
                RectTransform = { AnchorMin = $"0.1 {yPos}", AnchorMax = $"0.2 {yPos + 0.04f}" },
                Button = { Color = value ? "0.2 0.7 0.2 0.9" : "0.7 0.2 0.2 0.9", Command = command },
                Text = { Text = value ? "ON" : "OFF", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, UIMain);
            
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = $"0.22 {yPos}", AnchorMax = $"0.9 {yPos + 0.04f}" },
                Text = { Text = label, FontSize = 12, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" }
            }, UIMain);
        }

        private void DestroyUI(BasePlayer player)
        {
            CuiHelper.DestroyUi(player, UIMain);
        }

        #endregion

        #region Console Commands

        [ConsoleCommand("pve.closeui")]
        private void CmdCloseUI(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;
            
            DestroyUI(player);
        }

        [ConsoleCommand("pve.saveconfig")]
        private void CmdSaveConfig(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null || !permission.UserHasPermission(player.UserIDString, "absolutepve.admin")) return;
            
            SaveConfig();
            SendReply(player, "Configuration saved!");
            DestroyUI(player);
        }

        [ConsoleCommand("pve.toggle")]
        private void CmdToggleSetting(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null || !permission.UserHasPermission(player.UserIDString, "absolutepve.admin")) return;
            
            string setting = arg.GetString(0).ToLower();
            
            if (setting == "general.sleepers")
                config.General.AllowDamageToSleepers = !config.General.AllowDamageToSleepers;
            else if (setting == "general.teammates")
                config.General.AllowDamageToTeammates = !config.General.AllowDamageToTeammates;
            else if (setting == "general.clanmembers")
                config.General.AllowDamageToClanMembers = !config.General.AllowDamageToClanMembers;
            else if (setting == "general.friends")
                config.General.AllowDamageToFriends = !config.General.AllowDamageToFriends;
            else if (setting == "general.melee")
                config.General.AllowMeleeDamageOnly = !config.General.AllowMeleeDamageOnly;
            else if (setting == "zones.monuments")
                config.Zones.AllowDamageInMonuments = !config.Zones.AllowDamageInMonuments;
            else if (setting == "zones.pvpzones")
                config.Zones.AllowDamageInPvpZones = !config.Zones.AllowDamageInPvpZones;
            else if (setting == "npc.trap")
                config.NPCDamage.BlockTrapDamage = !config.NPCDamage.BlockTrapDamage;
            else if (setting == "npc.heli")
                config.NPCDamage.BlockHeliDamage = !config.NPCDamage.BlockHeliDamage;
            else if (setting == "npc.bradley")
                config.NPCDamage.BlockBradleyDamage = !config.NPCDamage.BlockBradleyDamage;
            else if (setting == "npc.turret")
                config.NPCDamage.BlockTurretDamage = !config.NPCDamage.BlockTurretDamage;
            else if (setting == "npc.scientist")
                config.NPCDamage.BlockScientistDamage = !config.NPCDamage.BlockScientistDamage;
            else if (setting == "npc.animal")
                config.NPCDamage.BlockAnimalDamage = !config.NPCDamage.BlockAnimalDamage;
            else if (setting == "notifications.enable")
                config.Notifications.EnableNotifications = !config.Notifications.EnableNotifications;
            else if (setting == "notifications.ui")
                config.Notifications.UseUINotifications = !config.Notifications.UseUINotifications;
            else if (setting == "schedule.enable")
                config.Schedule.EnableScheduledPvp = !config.Schedule.EnableScheduledPvp;
            else if (setting == "raid.enable")
                config.Raid.EnablePvpDuringRaids = !config.Raid.EnablePvpDuringRaids;
            
            // Refresh the current tab
            if (setting.StartsWith("general."))
                ShowGeneralTab(player);
            else if (setting.StartsWith("zones."))
                ShowZonesTab(player);
            else if (setting.StartsWith("npc."))
                ShowNPCTab(player);
            else if (setting.StartsWith("notifications."))
                ShowNotificationsTab(player);
            else if (setting.StartsWith("schedule."))
                ShowScheduleTab(player);
            else if (setting.StartsWith("raid."))
                ShowRaidTab(player);
        }

        [ConsoleCommand("pve.slider")]
        private void CmdUpdateSlider(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null || !permission.UserHasPermission(player.UserIDString, "absolutepve.admin"))
                return;
            
            string setting = arg.GetString(0).ToLower();
            float value = arg.GetFloat(1);
            
            if (setting == "zones.radius")
                config.Zones.PvpRadiusAroundMonuments = value;
            else if (setting == "notifications.duration")
                config.Notifications.UIDuration = value;
            else if (setting == "schedule.starthour")
                config.Schedule.PvpStartHour = (int)value;
            else if (setting == "schedule.endhour")
                config.Schedule.PvpEndHour = (int)value;
            else if (setting == "raid.radius")
                config.Raid.PvpRadiusAroundRaid = value;
            else if (setting == "raid.duration")
                config.Raid.PvpDurationAfterRaid = value;
            
            // Refresh the current tab
            if (setting.StartsWith("zones."))
                ShowZonesTab(player);
            else if (setting.StartsWith("notifications."))
                ShowNotificationsTab(player);
            else if (setting.StartsWith("schedule."))
                ShowScheduleTab(player);
            else if (setting.StartsWith("raid."))
                ShowRaidTab(player);
        }

        #endregion
    }
}























