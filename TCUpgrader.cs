using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Plugins;
using Oxide.Game.Rust.Cui;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("TC Upgrader", "Frizzo420", "1.0.0")]
    [Description("Adds upgrade buttons to Tool Cupboards")]
    class TCUpgrader : RustPlugin
    {
        [PluginReference] private Plugin ImageLibrary;
        private const string PERMISSION_USE = "tcupgrader.use";
        private Dictionary<ulong, string> activeUIs = new Dictionary<ulong, string>();
        private Dictionary<string, string> imageCache = new Dictionary<string, string>();

        private const string WOOD_IMAGE = "wood";
        private const string STONE_IMAGE = "stones";
        private const string METAL_IMAGE = "metal.fragments";
        private const string ARMORED_IMAGE = "metal.refined";
        
        #region Configuration
        
        private Configuration config;
        
        private class Configuration
        {
            [JsonProperty("Config Version")]
            public string ConfigVersion { get; set; } = "1.0.0";
            
            // Resource Settings
            [JsonProperty("Upgrade Range")]
            public float UpgradeRange = 100f;
            
            [JsonProperty("Require Resources For Upgrades")]
            public bool RequireResourcesForUpgrades = true;
            
            [JsonProperty("Require Resources For Repairs")]
            public bool RequireResourcesForRepairs = true;
            
            [JsonProperty("Enable Cost Multiplier")]
            public bool EnableCostMultiplier = false;
            
            [JsonProperty("Cost Multiplier")]
            public float CostMultiplier = 1.0f;
            
            [JsonProperty("Repair Cost Multiplier")]
            public float RepairCostMultiplier = 0.5f;
            
            [JsonProperty("Allow Downgrading")]
            public bool AllowDowngrading = false;
            
            // Permission Settings
            [JsonProperty("Enable Permission Requirement")]
            public bool EnablePermission = true;
            
            [JsonProperty("Admin Permission")]
            public string AdminPermission = "tcupgrader.admin";
            
            // UI Positioning
            [JsonProperty("UI Position")]
            public string UIPosition = "top";
            
            [JsonProperty("UI Height")]
            public float UIHeight = 0.05f;
            
            [JsonProperty("UI Width")]
            public float UIWidth = 0.1f;
            
            [JsonProperty("UI Horizontal Offset")]
            public float UIHorizontalOffset = 0.25f; // Change this value to move left/right
                                                     // Negative values move left, positive values move right
                                                     // Try -0.1 to move left
            
            [JsonProperty("UI Vertical Offset")]
            public float UIVerticalOffset = -0.25f;  // Change this value to move up/down
                                                     // Positive values move UP
                                                     // Negative values move DOWN
                                                     // Try 0.1 to move up, -0.1 to move down
            
            // Button Settings
            [JsonProperty("Button Size")]
            public float ButtonSize = 0.27f;
            
            [JsonProperty("Button Spacing")]
            public float ButtonSpacing = 0.02f;
            
            [JsonProperty("Button Vertical Position")]
            public float ButtonVerticalPosition = 0.8f;
            
            [JsonProperty("Button Row Height")]
            public float ButtonRowHeight = 0.6f;
            
            // Title Settings
            [JsonProperty("Show Title")]
            public bool ShowTitle = true;
            
            [JsonProperty("Title Text")]
            public string TitleText = "Upgrade Building";
            
            [JsonProperty("Title Font Size")]
            public int TitleFontSize = 12;
            
            [JsonProperty("Title Height")]
            public float TitleHeight = 0.4f;
            
            // Button Colors
            [JsonProperty("Wood Button Color")]
            public string WoodButtonColor = "0.7 0.5 0.2 1";
            
            [JsonProperty("Stone Button Color")]
            public string StoneButtonColor = "0.5 0.5 0.5 1";
            
            [JsonProperty("Metal Button Color")]
            public string MetalButtonColor = "0.7 0.7 0.7 1";
            
            [JsonProperty("Armored Button Color")]
            public string ArmoredButtonColor = "0.3 0.3 0.3 1";
            
            // Background Settings
            [JsonProperty("Background Color")]
            public string BackgroundColor = "0.1 0.1 0.1 0.8";
            
            // Command Settings
            [JsonProperty("Enable Chat Command")]
            public bool EnableChatCommand = true;
            
            [JsonProperty("Chat Command")]
            public string ChatCommand = "tcupgrade";
            
            [JsonProperty("Position Command")]
            public string PositionCommand = "tcupgrader.position";
            
            [JsonProperty("Reload Command")]
            public string ReloadCommand = "tcupgrader.reload";
            
            // Repair Settings
            [JsonProperty("Enable Repair")]
            public bool EnableRepair = true;
            
            [JsonProperty("Repair Button Color")]
            public string RepairButtonColor = "0.2 0.6 0.2 1";
            
            [JsonProperty("Repair Command")]
            public string RepairCommand = "tcrepair";
            
            [JsonProperty("Show Repair Button")]
            public bool ShowRepairButton = true;
            
            // Sound Settings
            [JsonProperty("Enable Repair Sounds")]
            public bool EnableRepairSounds = true;
            
            [JsonProperty("Enable Upgrade Sounds")]
            public bool EnableUpgradeSounds = true;
            
            [JsonProperty("Sound Volume")]
            public float SoundVolume = 1.0f;
            
            // Message Settings
            [JsonProperty("Enable Custom Messages")]
            public bool EnableCustomMessages = true;
            
            [JsonProperty("Success Message Prefix")]
            public string SuccessMessagePrefix = "<color=#00FF00>[TC Upgrader]</color> ";
            
            [JsonProperty("Error Message Prefix")]
            public string ErrorMessagePrefix = "<color=#FF0000>[TC Upgrader]</color> ";
            
            [JsonProperty("No Permission Message")]
            public string NoPermissionMessage = "You don't have permission to use this command.";
            
            [JsonProperty("Upgrade Success Message")]
            public string UpgradeSuccessMessage = "Successfully upgraded {0} building blocks to {1}!";
            
            [JsonProperty("Upgrade Failed Message")]
            public string UpgradeFailedMessage = "Failed to upgrade any building blocks to {0}.";
            
            [JsonProperty("Not Enough Resources Message")]
            public string NotEnoughResourcesMessage = "You don't have enough resources to upgrade to {0}.";
            
            [JsonProperty("No Blocks Found Message")]
            public string NoBlocksFoundMessage = "No building blocks found within range.";
            
            [JsonProperty("Repair Success Message")]
            public string RepairSuccessMessage = "Successfully repaired {0} building blocks!";
            
            // Message Image Settings
            [JsonProperty("Use Message Images")]
            public bool UseMessageImages = false;
            
            [JsonProperty("Success Message Image URL")]
            public string SuccessMessageImageUrl = "https://i.imgur.com/JZlVGaz.png";
            
            [JsonProperty("Error Message Image URL")]
            public string ErrorMessageImageUrl = "https://i.imgur.com/SgXSyQi.png";
            
            [JsonProperty("Message Image Size")]
            public int MessageImageSize = 20;
            
            // Button Text Settings
            [JsonProperty("Button Text Size")]
            public int ButtonTextSize = 8;
            
            // Custom Image Settings
            [JsonProperty("Use Custom Images")]
            public bool UseCustomImages = false;

            [JsonProperty("Custom Wood Image URL")]
            public string CustomWoodImageUrl = "https://rustlabs.com/img/items180/wood.png";

            [JsonProperty("Custom Stone Image URL")]
            public string CustomStoneImageUrl = "https://rustlabs.com/img/items180/stones.png";

            [JsonProperty("Custom Metal Image URL")]
            public string CustomMetalImageUrl = "https://rustlabs.com/img/items180/metal.fragments.png";

            [JsonProperty("Custom Armored Image URL")]
            public string CustomArmoredImageUrl = "https://rustlabs.com/img/items180/metal.refined.png";

            [JsonProperty("Custom Repair Image URL")]
            public string CustomRepairImageUrl = "https://rustlabs.com/img/items180/hammer.png";

            [JsonProperty("Downgrading Disabled Message")]
            public string DowngradingDisabledMessage = "Downgrading is disabled on this server.";
        }
        
        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) throw new Exception("Could not read config file");
                SaveConfig();
            }
            catch
            {
                PrintError("Configuration file is corrupt, creating new config file");
                LoadDefaultConfig();
            }
        }
        
        protected override void LoadDefaultConfig()
        {
            string pluginVersion = Version.ToString();
            config = new Configuration
            {
                ConfigVersion = pluginVersion,
                
                // Resource Settings
                UpgradeRange = 100f,
                RequireResourcesForUpgrades = true,
                RequireResourcesForRepairs = true,
                EnableCostMultiplier = false,
                CostMultiplier = 1.0f,
                RepairCostMultiplier = 0.5f,
                AllowDowngrading = false,
                
                // Permission Settings
                EnablePermission = true,
                AdminPermission = "tcupgrader.admin",
                
                // UI Positioning - UPDATED DEFAULT VALUES
                UIPosition = "top",
                UIHeight = 0.05f,
                UIWidth = 0.1f,
                UIHorizontalOffset = 0.3f,
                UIVerticalOffset = -0.24f,
                
                // Button Settings
                ButtonSize = 0.27f,
                ButtonSpacing = 0.02f,
                ButtonVerticalPosition = 0.8f,
                ButtonRowHeight = 0.6f,
                
                // Title Settings
                ShowTitle = true,
                TitleText = "Upgrade Building",
                TitleFontSize = 12,
                TitleHeight = 0.4f,
                
                // Button Colors - UPDATED TO GREEN
                WoodButtonColor = "0.2 0.6 0.2 1",
                StoneButtonColor = "0.2 0.6 0.2 1",
                MetalButtonColor = "0.2 0.6 0.2 1",
                ArmoredButtonColor = "0.2 0.6 0.2 1",
                BackgroundColor = "0.1 0.1 0.1 0.8",
                
                // Command Settings
                EnableChatCommand = true,
                ChatCommand = "tcupgrade",
                PositionCommand = "tcupgrader.position",
                ReloadCommand = "tcupgrader.reload",
                
                // Repair Settings
                EnableRepair = true,
                RepairButtonColor = "0.2 0.6 0.2 1",
                RepairCommand = "tcrepair",
                ShowRepairButton = true,
                
                // Sound Settings
                EnableRepairSounds = true,
                EnableUpgradeSounds = true,
                SoundVolume = 1.0f,
                
                // Message Settings
                EnableCustomMessages = true,
                SuccessMessagePrefix = "<color=#00FF00>[TC Upgrader]</color> ",
                ErrorMessagePrefix = "<color=#FF0000>[TC Upgrader]</color> ",
                NoPermissionMessage = "You don't have permission to use this command.",
                UpgradeSuccessMessage = "Successfully upgraded {0} building blocks to {1}!",
                UpgradeFailedMessage = "Failed to upgrade any building blocks to {0}.",
                NotEnoughResourcesMessage = "You don't have enough resources to upgrade to {0}.",
                NoBlocksFoundMessage = "No building blocks found within range.",
                RepairSuccessMessage = "Successfully repaired {0} building blocks!",
                DowngradingDisabledMessage = "Downgrading is disabled on this server.",
                
                // Message Image Settings
                UseMessageImages = false,
                SuccessMessageImageUrl = "https://i.imgur.com/JZlVGaz.png",
                ErrorMessageImageUrl = "https://i.imgur.com/SgXSyQi.png",
                MessageImageSize = 20,
                
                // Button Text Settings
                ButtonTextSize = 8,
                
                // Custom Image Settings
                UseCustomImages = false,
                CustomWoodImageUrl = "https://rustlabs.com/img/items180/wood.png",
                CustomStoneImageUrl = "https://rustlabs.com/img/items180/stones.png",
                CustomMetalImageUrl = "https://rustlabs.com/img/items180/metal.fragments.png",
                CustomArmoredImageUrl = "https://rustlabs.com/img/items180/metal.refined.png",
                CustomRepairImageUrl = "https://rustlabs.com/img/items180/hammer.png"
            };
            SaveConfig();
        }
        
        protected override void SaveConfig()
        {
            Config.WriteObject(config, true);
        }
        
        #endregion
        
        private void Init()
        {
            permission.RegisterPermission(PERMISSION_USE, this);
            permission.RegisterPermission(config.AdminPermission, this);
            
            if (config.EnableChatCommand)
            {
                cmd.AddChatCommand(config.ChatCommand, this, "CommandUpgrade");
                cmd.AddChatCommand(config.RepairCommand, this, "CommandRepair");
                cmd.AddChatCommand(config.PositionCommand, this, "CommandPosition");
                cmd.AddChatCommand(config.ReloadCommand, this, "CommandReload");
            }
            
            // Register console commands
            cmd.AddConsoleCommand("tcupgrade", this, "ConsoleCommandUpgrade");
            cmd.AddConsoleCommand("tcrepair", this, "ConsoleCommandRepair");
        }
        
        private void OnServerInitialized()
        {
            if (ImageLibrary == null)
            {
                PrintError("ImageLibrary is not loaded! Plugin will not function correctly.");
                return;
            }
            
            // Register images with ImageLibrary
            RegisterImages();
            
            // Load foundation grades
            LoadFoundationGrades();
            
            // Add periodic check for orphaned UIs
            timer.Every(30f, CheckForOrphanedUIs);
            
            PrintWarning("TC Upgrader initialized");
        }
        
        private void RegisterImages()
        {
            if (ImageLibrary == null) return;
            
            // Check if images already exist in ImageLibrary
            if (string.IsNullOrEmpty((string)ImageLibrary.Call("GetImage", WOOD_IMAGE)))
            {
                // Use default RustLabs images if custom images are not enabled
                if (!config.UseCustomImages)
                {
                    // Use default RustLabs images
                    ImageLibrary.Call("AddImage", "https://rustlabs.com/img/items180/wood.png", WOOD_IMAGE);
                    ImageLibrary.Call("AddImage", "https://rustlabs.com/img/items180/stones.png", STONE_IMAGE);
                    ImageLibrary.Call("AddImage", "https://rustlabs.com/img/items180/metal.fragments.png", METAL_IMAGE);
                    ImageLibrary.Call("AddImage", "https://rustlabs.com/img/items180/metal.refined.png", ARMORED_IMAGE);
                    ImageLibrary.Call("AddImage", "https://rustlabs.com/img/items180/hammer.png", "hammer");
                }
                else
                {
                    // Use custom images from config
                    ImageLibrary.Call("AddImage", config.CustomWoodImageUrl, WOOD_IMAGE);
                    ImageLibrary.Call("AddImage", config.CustomStoneImageUrl, STONE_IMAGE);
                    ImageLibrary.Call("AddImage", config.CustomMetalImageUrl, METAL_IMAGE);
                    ImageLibrary.Call("AddImage", config.CustomArmoredImageUrl, ARMORED_IMAGE);
                    ImageLibrary.Call("AddImage", config.CustomRepairImageUrl, "hammer");
                }
                
                PrintWarning("Registering images with ImageLibrary...");
            }
            else
            {
                PrintWarning("Images already registered with ImageLibrary");
            }
        }
        
        private string GetImage(string imageName)
        {
            if (ImageLibrary == null) return string.Empty;
            
            if (imageCache.ContainsKey(imageName))
                return imageCache[imageName];
            
            string imageId = (string)ImageLibrary.Call("GetImage", imageName);
            if (!string.IsNullOrEmpty(imageId))
            {
                imageCache[imageName] = imageId;
                return imageId;
            }
            
            return string.Empty;
        }
        
        private void OnLootEntity(BasePlayer player, BuildingPrivlidge privilege)
        {
            if (privilege == null || player == null) return;
            
            if (config.EnablePermission && !permission.UserHasPermission(player.UserIDString, PERMISSION_USE))
                return;
            
            timer.Once(0.5f, () => CreateUI(player));
        }
        
        private void OnPlayerLootEnd(PlayerLoot inventory)
        {
            if (inventory == null) return;
            
            BasePlayer player = inventory.GetComponent<BasePlayer>();
            if (player == null) return;
            
            // Make sure we're destroying the UI properly
            if (activeUIs.ContainsKey(player.userID))
            {
                DestroyUI(player);
            }
        }

        // Add this new method to check for orphaned UIs
        private void CheckForOrphanedUIs()
        {
            foreach (var entry in activeUIs.ToList())
            {
                var player = BasePlayer.FindByID(entry.Key);
                if (player == null || player.inventory.loot.entitySource == null)
                {
                    if (player != null)
                    {
                        DestroyUI(player);
                    }
                    else
                    {
                        activeUIs.Remove(entry.Key);
                    }
                }
            }
        }
        
        private void CreateUI(BasePlayer player)
        {
            if (player == null) return;
            
            CuiElementContainer container = new CuiElementContainer();
            
            // Calculate UI position based on config - slight adjustment left
            float xMin = 0.5f - (config.UIWidth / 2) + config.UIHorizontalOffset - 0.05f;
            float xMax = 0.5f + (config.UIWidth / 2) + config.UIHorizontalOffset - 0.05f;
            float yMin = 0.5f - (config.UIHeight / 2) + config.UIVerticalOffset;
            float yMax = 0.5f + (config.UIHeight / 2) + config.UIVerticalOffset;
            
            // Adjust position based on UI position setting
            switch (config.UIPosition.ToLower())
            {
                case "top":
                    yMin = 1 - config.UIHeight - 0.01f;
                    yMax = 1 - 0.01f;
                    break;
                case "bottom":
                    yMin = 0.01f;
                    yMax = 0.01f + config.UIHeight;
                    break;
                case "left":
                    xMin = 0.01f;
                    xMax = 0.01f + config.UIWidth;
                    yMin = 0.5f - (config.UIHeight / 2);
                    yMax = 0.5f + (config.UIHeight / 2);
                    break;
                case "right":
                    xMin = 1 - config.UIWidth - 0.01f;
                    xMax = 1 - 0.01f;
                    yMin = 0.5f - (config.UIHeight / 2);
                    yMax = 0.5f + (config.UIHeight / 2);
                    break;
                default:
                    // Center position with offset - slight adjustment left
                    xMin = 0.5f - (config.UIWidth / 2) + config.UIHorizontalOffset - 0.05f;
                    xMax = 0.5f + (config.UIWidth / 2) + config.UIHorizontalOffset - 0.05f;
                    yMin = 0.5f - (config.UIHeight / 2) + config.UIVerticalOffset;
                    yMax = 0.5f + (config.UIHeight / 2) + config.UIVerticalOffset;
                    break;
            }
            
            // Apply vertical offset
            yMin += config.UIVerticalOffset;
            yMax += config.UIVerticalOffset;
            
            // Create main panel
            container.Add(new CuiPanel
            {
                RectTransform = { AnchorMin = $"{xMin} {yMin}", AnchorMax = $"{xMax} {yMax}" },
                Image = { Color = config.BackgroundColor }
            }, "Overlay", "TCUpgrader_Main");

            // Calculate button positions
            float buttonWidth = config.ButtonSize;
            float spacing = config.ButtonSpacing;
            
            // Determine number of buttons (4 upgrade buttons + optional repair button)
            int buttonCount = config.ShowRepairButton && config.EnableRepair ? 5 : 4;
            float totalWidth = (buttonWidth * buttonCount) + (spacing * (buttonCount - 1));
            float startX = (1 - totalWidth) / 2;
            
            // Add title if enabled
            float titleHeight = config.ShowTitle ? config.TitleHeight : 0f;
            if (config.ShowTitle)
            {
                container.Add(new CuiLabel
                {
                    RectTransform = { AnchorMin = $"0 {1 - titleHeight}", AnchorMax = "1 1" },
                    Text = { Text = config.TitleText, FontSize = config.TitleFontSize, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, "TCUpgrader_Main");
            }
            
            // Calculate button vertical position
            float buttonYMin = 0;
            float buttonYMax = 0;
            
            if (config.ShowTitle)
            {
                // If title is shown, position buttons below it
                buttonYMin = 0;
                buttonYMax = 1 - titleHeight;
            }
            else
            {
                // If no title, use full height
                buttonYMin = 0;
                buttonYMax = 1;
            }
            
            // Create upgrade buttons - ALL SET TO GREEN
            // Wood Button
            CreateUpgradeButton(container, "Wood", WOOD_IMAGE, BuildingGrade.Enum.Wood, 
                startX, startX + buttonWidth, buttonYMin, buttonYMax, "0.2 0.6 0.2 1");
            
            // Stone Button
            CreateUpgradeButton(container, "Stone", STONE_IMAGE, BuildingGrade.Enum.Stone, 
                startX + (buttonWidth + spacing), startX + buttonWidth * 2 + spacing, buttonYMin, buttonYMax, "0.2 0.6 0.2 1");
            
            // Metal Button
            CreateUpgradeButton(container, "Metal", METAL_IMAGE, BuildingGrade.Enum.Metal, 
                startX + (buttonWidth + spacing) * 2, startX + buttonWidth * 3 + spacing * 2, buttonYMin, buttonYMax, "0.2 0.6 0.2 1");
            
            // Armored Button
            CreateUpgradeButton(container, "Armored", ARMORED_IMAGE, BuildingGrade.Enum.TopTier, 
                startX + (buttonWidth + spacing) * 3, startX + buttonWidth * 4 + spacing * 3, buttonYMin, buttonYMax, "0.2 0.6 0.2 1");

            // Add Repair Button if enabled - ALSO GREEN
            if (config.ShowRepairButton && config.EnableRepair)
            {
                CreateRepairButton(container, "Repair", 
                    startX + (buttonWidth + spacing) * 4, startX + buttonWidth * 5 + spacing * 4, buttonYMin, buttonYMax, "0.2 0.6 0.2 1");
            }

            string uiId = CuiHelper.GetGuid();
            activeUIs[player.userID] = uiId;
            
            CuiHelper.AddUi(player, container);
        }

        private void CreateUpgradeButton(CuiElementContainer container, string name, string image, BuildingGrade.Enum grade, 
            float xMin, float xMax, float yMin, float yMax, string color)
        {
            string buttonName = $"TCUpgrader_Button_{grade}";
            
            // Button panel
            container.Add(new CuiPanel
            {
                RectTransform = { AnchorMin = $"{xMin} {yMin}", AnchorMax = $"{xMax} {yMax}" },
                Image = { Color = color }
            }, "TCUpgrader_Main", buttonName);

            // Image
            container.Add(new CuiElement
            {
                Parent = buttonName,
                Components =
                {
                    new CuiRawImageComponent { Png = GetImage(image), Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.1 0.3", AnchorMax = "0.9 0.9" }
                }
            });

            // Label
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 0.3" },
                Text = { Text = name, FontSize = config.ButtonTextSize, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, buttonName);

            // Button - updated to use console command
            container.Add(new CuiButton
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                Button = { Color = "0 0 0 0", Command = $"tcupgrade {grade.ToString().ToLower()}" },
                Text = { Text = "" }
            }, buttonName);
        }

        private void CreateRepairButton(CuiElementContainer container, string name, 
            float xMin, float xMax, float yMin, float yMax, string color)
        {
            string buttonName = "TCUpgrader_Button_Repair";
            
            // Button panel
            container.Add(new CuiPanel
            {
                RectTransform = { AnchorMin = $"{xMin} {yMin}", AnchorMax = $"{xMax} {yMax}" },
                Image = { Color = color }
            }, "TCUpgrader_Main", buttonName);
            
            // Add repair icon
            container.Add(new CuiElement
            {
                Parent = buttonName,
                Components =
                {
                    new CuiRawImageComponent { Png = GetImage("hammer"), Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.1 0.3", AnchorMax = "0.9 0.9" }
                }
            });
            
            // Add button text (single label)
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 0.3" },
                Text = { Text = name, FontSize = config.ButtonTextSize, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, buttonName);
            
            // Add button action
            container.Add(new CuiButton
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                Button = { Command = "tcrepair", Color = "0 0 0 0" },
                Text = { Text = "" }
            }, buttonName);
        }

        private void DestroyUI(BasePlayer player)
        {
            if (player == null) return;
            
            // Ensure we're destroying the UI element
            CuiHelper.DestroyUi(player, "TCUpgrader_Main");
            activeUIs.Remove(player.userID);
        }

        [ChatCommand("tcupgrade")]
        private void CommandUpgrade(BasePlayer player, string command, string[] args)
        {
            if (player == null) return;
            
            if (config.EnablePermission && !permission.UserHasPermission(player.UserIDString, PERMISSION_USE))
            {
                SendReply(player, config.ErrorMessagePrefix + config.NoPermissionMessage);
                return;
            }
            
            if (args.Length < 1)
            {
                SendReply(player, config.ErrorMessagePrefix + "Usage: /tcupgrade <wood|stone|metal|armored>");
                return;
            }
            
            BuildingGrade.Enum grade;
            switch (args[0].ToLower())
            {
                case "0":
                case "wood":
                    grade = BuildingGrade.Enum.Wood;
                    break;
                case "1":
                case "stone":
                    grade = BuildingGrade.Enum.Stone;
                    break;
                case "2":
                case "metal":
                    grade = BuildingGrade.Enum.Metal;
                    break;
                case "3":
                case "armored":
                case "hqm":
                    grade = BuildingGrade.Enum.TopTier;
                    break;
                default:
                    SendReply(player, config.ErrorMessagePrefix + "Invalid grade. Use: wood, stone, metal, or armored");
                    return;
            }
            
            UpgradeNearbyBlocks(player, grade);
        }

        [ChatCommand("tcrepair")]
        private void CommandRepair(BasePlayer player, string command, string[] args)
        {
            if (player == null) return;
            
            if (config.EnablePermission && !permission.UserHasPermission(player.UserIDString, PERMISSION_USE))
            {
                SendReply(player, config.ErrorMessagePrefix + config.NoPermissionMessage);
                return;
            }
            
            RepairNearbyBlocks(player);
        }

        [ChatCommand("tcupgrader.reload")]
        private void CommandReload(BasePlayer player, string command, string[] args)
        {
            if (player == null || !permission.UserHasPermission(player.UserIDString, config.AdminPermission))
            {
                SendReply(player, config.ErrorMessagePrefix + config.NoPermissionMessage);
                return;
            }
            
            LoadConfig();
            SendReply(player, config.SuccessMessagePrefix + "Configuration reloaded!");
        }

        [ChatCommand("tcupgrader.position")]
        private void CommandPosition(BasePlayer player, string command, string[] args)
        {
            if (player == null || !permission.UserHasPermission(player.UserIDString, config.AdminPermission))
            {
                SendReply(player, config.ErrorMessagePrefix + config.NoPermissionMessage);
                return;
            }
            
            if (args.Length < 1)
            {
                SendReply(player, config.ErrorMessagePrefix + "Usage: /tcupgrader.position <top|bottom|left|right|horizontal|vertical> [value]");
                return;
            }
            
            string param = args[0].ToLower();
            
            // Handle preset positions
            if (param == "top" || param == "bottom" || param == "left" || param == "right")
            {
                config.UIPosition = param;
                SaveConfig();
                SendReply(player, config.SuccessMessagePrefix + $"UI position set to {param}");
                return;
            }
            
            // Handle offset adjustments
            if ((param == "horizontal" || param == "vertical") && args.Length >= 2)
            {
                float value;
                if (!float.TryParse(args[1], out value))
                {
                    SendReply(player, config.ErrorMessagePrefix + "Value must be a number");
                    return;
                }
                
                if (param == "horizontal")
                {
                    config.UIHorizontalOffset = value;
                    SendReply(player, config.SuccessMessagePrefix + $"UI horizontal offset set to {value}");
                }
                else // vertical
                {
                    config.UIVerticalOffset = value;
                    SendReply(player, config.SuccessMessagePrefix + $"UI vertical offset set to {value}");
                }
                
                SaveConfig();
                
                // Refresh UI for all players with open UIs
                foreach (var entry in activeUIs)
                {
                    var p = BasePlayer.FindByID(entry.Key);
                    if (p != null)
                    {
                        DestroyUI(p);
                        CreateUI(p);
                    }
                }
                
                return;
            }
            
            // If we get here, the command was invalid
            SendReply(player, config.ErrorMessagePrefix + "Usage: /tcupgrader.position <top|bottom|left|right|horizontal|vertical> [value]");
        }

        private void UpgradeNearbyBlocks(BasePlayer player, BuildingGrade.Enum targetGrade)
        {
            // Find nearby building blocks
            List<BuildingBlock> blocks = new List<BuildingBlock>();
            Vis.Entities(player.transform.position, config.UpgradeRange, blocks, LayerMask.GetMask("Construction"));
            
            if (blocks.Count == 0)
            {
                SendReply(player, config.ErrorMessagePrefix + config.NoBlocksFoundMessage);
                return;
            }
            
            // Check if this is a downgrade attempt
            bool isDowngradeAttempt = false;
            foreach (var block in blocks)
            {
                if (block != null && !block.IsDestroyed && (int)block.grade > (int)targetGrade)
                {
                    isDowngradeAttempt = true;
                    break;
                }
            }
            
            // Only block downgrading if it's disabled in config
            if (isDowngradeAttempt && !config.AllowDowngrading)
            {
                SendReply(player, config.ErrorMessagePrefix + config.DowngradingDisabledMessage);
                return;
            }
            
            // Track resources and blocks
            Dictionary<ItemDefinition, int> missingItems = new Dictionary<ItemDefinition, int>();
            List<Item> collect = new List<Item>();
            Dictionary<int, float> takeOutItems = new Dictionary<int, float>();
            int success = 0;
            bool anyAttempted = false;
            
            // Process each block
            foreach (var buildingBlock in blocks)
            {
                if (buildingBlock == null || buildingBlock.IsDestroyed)
                    continue;
                    
                // Skip if already at target grade
                if (buildingBlock.grade == targetGrade)
                    continue;
                
                // Check if this is a downgrade for this specific block
                bool isBlockDowngrade = (int)buildingBlock.grade > (int)targetGrade;
                
                // Skip if downgrading is not allowed and this is a downgrade
                if (!config.AllowDowngrading && isBlockDowngrade)
                    continue;
                
                anyAttempted = true;
                    
                // Check if player has building privilege
                if (player.IsBuildingBlocked(buildingBlock.WorldSpaceBounds()))
                    continue;
                    
                // Check if block can be upgraded/downgraded to target grade
                if (!CanUpgradeToGrade(buildingBlock, player, targetGrade, config.RequireResourcesForUpgrades, missingItems, collect, isBlockDowngrade))
                    continue;
                    
                // Perform the upgrade/downgrade
                var oldGrade = buildingBlock.grade;
                SetBuildingBlockGrade(buildingBlock, targetGrade);
                Interface.CallHook("OnStructureGradeUpdated", buildingBlock, player, oldGrade, targetGrade);
                success++;
            }
            
            // Take resources from player
            foreach (var item in collect)
            {
                takeOutItems[item.info.itemid] = takeOutItems.ContainsKey(item.info.itemid) ? 
                    takeOutItems[item.info.itemid] + item.amount : item.amount;
                item.Remove();
            }
            
            // Show resource usage in chat
            foreach (var entry in takeOutItems)
            {
                player.Command("note.inv " + entry.Key + " " + entry.Value * -1f);
            }
            
            // Send result message to player
            if (missingItems.Count > 0)
            {
                StringBuilder stringBuilder = new StringBuilder();
                foreach (var entry in missingItems)
                {
                    stringBuilder.AppendLine($"* <color=#FF1919>{entry.Key.displayName.translated}</color> x{entry.Value}");
                }
                string missingResources = stringBuilder.ToString();
                
                if (success > 0)
                {
                    SendReply(player, config.SuccessMessagePrefix + $"{success} building blocks were {(isDowngradeAttempt ? "downgraded" : "upgraded")}. Some buildings cannot be modified, you are missing: \n{missingResources}");
                }
                else
                {
                    SendReply(player, config.ErrorMessagePrefix + $"None of the buildings were {(isDowngradeAttempt ? "downgraded" : "upgraded")}. You are missing: \n{missingResources}");
                }
            }
            else
            {
                if (success > 0)
                {
                    // Use "upgraded" for upgrades and "downgraded" for downgrades
                    string actionWord = isDowngradeAttempt ? "downgraded" : "upgraded";
                    SendReply(player, config.SuccessMessagePrefix + $"Successfully {actionWord} {success} building blocks to {GetGradeName(targetGrade)}!");
                }
                else if (anyAttempted)
                {
                    // Only show failed message if any blocks were actually attempted
                    string actionWord = isDowngradeAttempt ? "downgrade" : "upgrade";
                    SendReply(player, config.ErrorMessagePrefix + $"Failed to {actionWord} any building blocks to {GetGradeName(targetGrade)}.");
                }
            }
            
            // Play sound if enabled
            if (config.EnableUpgradeSounds && success > 0)
            {
                Effect.server.Run("assets/bundled/prefabs/fx/build/promote_" + targetGrade.ToString().ToLower() + ".prefab", player.transform.position);
            }
        }

        // Modified to handle downgrades
        private bool CanUpgradeToGrade(BuildingBlock buildingBlock, BasePlayer player, BuildingGrade.Enum targetGrade, bool pay, Dictionary<ItemDefinition, int> missingItems, List<Item> collect, bool isDowngrade = false)
        {
            if (player == null || !player.CanInteract())
                return false;
                
            var constructionGrade = buildingBlock.blockDefinition.GetGrade(targetGrade, 0);
            if (constructionGrade == null)
                return false;
                
            // Check for hooks and restrictions
            if (Interface.CallHook("OnStructureUpgrade", buildingBlock, player, targetGrade, 0) != null)
                return false;
                
            if (buildingBlock.SecondsSinceAttacked < 30f)
                return false;
                
            // Skip this check for downgrades when allowed
            if (!isDowngrade && !buildingBlock.CanChangeToGrade(targetGrade, 0, player))
                return false;
                
            if (!HasAccess(player, buildingBlock))
                return false;
                
            // Check if player can afford the upgrade
            if (pay && !permission.UserHasPermission(player.UserIDString, "tcupgrader.nocost"))
            {
                if (!CanAffordUpgrade(buildingBlock, constructionGrade, player, targetGrade, missingItems))
                    return false;
                    
                PayForUpgrade(buildingBlock, constructionGrade, player, collect);
            }
            
            return true;
        }

        private bool CanAffordUpgrade(BuildingBlock buildingBlock, ConstructionGrade constructionGrade, BasePlayer player, BuildingGrade.Enum grade, Dictionary<ItemDefinition, int> missingItems)
        {
            var obj = Interface.CallHook("CanAffordUpgrade", player, buildingBlock, grade);
            if (obj is bool)
                return (bool)obj;
                
            bool flag = true;
            foreach (var item in constructionGrade.CostToBuild())
            {
                int amount = (int)item.amount;
                if (config.EnableCostMultiplier)
                    amount = Mathf.CeilToInt(amount * config.CostMultiplier);
                    
                var missingAmount = amount - player.inventory.GetAmount(item.itemid);
                if (missingAmount > 0f)
                {
                    flag = false;
                    if (!missingItems.ContainsKey(item.itemDef))
                        missingItems[item.itemDef] = 0;
                        
                    missingItems[item.itemDef] += (int)missingAmount;
                }
            }
            return flag;
        }

        private void PayForUpgrade(BuildingBlock buildingBlock, ConstructionGrade constructionGrade, BasePlayer player, List<Item> collect)
        {
            if (Interface.CallHook("OnPayForUpgrade", player, buildingBlock, constructionGrade) != null)
                return;
                
            foreach (var item in constructionGrade.CostToBuild())
            {
                int amount = (int)item.amount;
                if (config.EnableCostMultiplier)
                    amount = Mathf.CeilToInt(amount * config.CostMultiplier);
                    
                player.inventory.Take(collect, item.itemid, amount);
            }
        }

        private bool HasAccess(BasePlayer player, BuildingBlock buildingBlock)
        {
            // Check if player has building privilege
            if (player.IsBuildingBlocked(buildingBlock.transform.position, buildingBlock.transform.rotation, buildingBlock.bounds))
                return false;
                
            // Check if block is upgrade blocked
            if (buildingBlock.IsUpgradeBlocked())
                return false;
                
            return true;
        }

        private void SetBuildingBlockGrade(BuildingBlock buildingBlock, BuildingGrade.Enum targetGrade)
        {
            buildingBlock.skinID = 0; // to avoid upgrade/downgrade to twig when using building skin
            buildingBlock.SetGrade(targetGrade);
            buildingBlock.SetHealthToMax();
            buildingBlock.StartBeingRotatable();
            buildingBlock.SendNetworkUpdate();
            buildingBlock.UpdateSkin();
            buildingBlock.ResetUpkeepTime();
            buildingBlock.UpdateSurroundingEntities();
            BuildingManager.server.GetBuilding(buildingBlock.buildingID)?.Dirty();
            
            // Play effect
            if (targetGrade > BuildingGrade.Enum.Twigs)
            {
                Effect.server.Run("assets/bundled/prefabs/fx/build/promote_" + targetGrade.ToString().ToLower() + ".prefab", buildingBlock, 0u, Vector3.zero, Vector3.zero);
            }
        }

        // Dictionary to store foundation "virtual" grades
        private Dictionary<ulong, BuildingGrade.Enum> foundationGrades = new Dictionary<ulong, BuildingGrade.Enum>();

        private void StoreFoundationGrade(NetworkableId entityId, BuildingGrade.Enum grade)
        {
            foundationGrades[entityId.Value] = grade;
        }

        private BuildingGrade.Enum GetStoredFoundationGrade(NetworkableId entityId)
        {
            if (foundationGrades.TryGetValue(entityId.Value, out BuildingGrade.Enum grade))
                return grade;
            
            return BuildingGrade.Enum.Wood; // Default to wood if not found
        }

        // Override OnEntityTakeDamage to handle foundation damage based on their "virtual" grade
        [HookMethod("OnEntityTakeDamage")]
        private object OnEntityTakeDamage(BaseCombatEntity entity, HitInfo info)
        {
            if (entity == null || info == null) return null;
            
            BuildingBlock block = entity as BuildingBlock;
            if (block != null && block.ShortPrefabName.Contains("foundation") && foundationGrades.ContainsKey(block.net.ID.Value))
            {
                // Get the stored "virtual" grade
                BuildingGrade.Enum virtualGrade = foundationGrades[block.net.ID.Value];
                
                // Apply damage reduction based on the virtual grade
                float damageMultiplier = 1.0f;
                switch (virtualGrade)
                {
                    case BuildingGrade.Enum.Wood:
                        damageMultiplier = 1.0f;
                        break;
                    case BuildingGrade.Enum.Stone:
                        damageMultiplier = 0.2f; // 5x stronger
                        break;
                    case BuildingGrade.Enum.Metal:
                        damageMultiplier = 0.1f; // 10x stronger
                        break;
                    case BuildingGrade.Enum.TopTier:
                        damageMultiplier = 0.05f; // 20x stronger
                        break;
                }
                
                // Reduce damage
                info.damageTypes.ScaleAll(damageMultiplier);
                
                return null;
            }
            
            return null;
        }

        // Save and load foundation grades
        private void OnServerSave()
        {
            SaveFoundationGrades();
        }

        // We'll use Oxide hooks instead of overriding OnServerInitialized
        [HookMethod("OnServerInitialized")]
        private void OnServerInitializedHook()
        {
            // Load foundation grades
            LoadFoundationGrades();
        }

        private void SaveFoundationGrades()
        {
            Interface.Oxide.DataFileSystem.WriteObject("TCUpgrader_FoundationGrades", foundationGrades);
        }

        private void LoadFoundationGrades()
        {
            foundationGrades = Interface.Oxide.DataFileSystem.ReadObject<Dictionary<ulong, BuildingGrade.Enum>>("TCUpgrader_FoundationGrades") ?? new Dictionary<ulong, BuildingGrade.Enum>();
        }

        private bool UpgradeFoundation(BuildingBlock block, BuildingGrade.Enum grade)
        {
            if (block == null) return false;
            
            // Store original values
            string originalPrefabName = block.PrefabName;
            string originalShortName = block.ShortPrefabName;
            
            // Apply the grade change
            block.SetGrade(grade);
            block.health = block.MaxHealth();
            
            // Check if the prefab name changed (indicating it turned into a floor)
            if (!block.ShortPrefabName.Contains("foundation") && originalShortName.Contains("foundation"))
            {
                PrintWarning($"Foundation conversion detected: {originalShortName} -> {block.ShortPrefabName}");
                
                try
                {
                    // Try to fix by directly setting the prefab name field
                    var prefabNameField = block.GetType().GetField("_prefabName", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
                    if (prefabNameField != null)
                    {
                        // Construct the correct prefab name based on the original and the new grade
                        string correctPrefabName = originalPrefabName;
                        if (grade != BuildingGrade.Enum.Wood)
                        {
                            // Add the grade suffix for non-wood grades
                            string gradeSuffix = grade.ToString().ToLower();
                            // Remove .prefab, add .grade.prefab
                            correctPrefabName = correctPrefabName.Replace(".prefab", $".{gradeSuffix}.prefab");
                        }
                        
                        prefabNameField.SetValue(block, correctPrefabName);
                        PrintWarning($"Set prefab name to: {correctPrefabName}");
                    }
                    
                    // Force update
                    block.SendNetworkUpdate();
                }
                catch (Exception ex)
                {
                    PrintError($"Error fixing foundation prefab: {ex.Message}");
                }
            }
            
            return true;
        }

        private void RepairNearbyBlocks(BasePlayer player)
        {
            // Find nearby building blocks
            List<BuildingBlock> blocks = new List<BuildingBlock>();
            Vis.Entities(player.transform.position, config.UpgradeRange, blocks, LayerMask.GetMask("Construction"));
            
            if (blocks.Count == 0)
            {
                SendReply(player, config.ErrorMessagePrefix + config.NoBlocksFoundMessage);
                return;
            }
            
            // Find the tool cupboard
            BuildingPrivlidge toolCupboard = null;
            List<BuildingPrivlidge> cupboards = new List<BuildingPrivlidge>();
            Vis.Entities(player.transform.position, 3f, cupboards);
            
            if (cupboards.Count > 0)
            {
                toolCupboard = cupboards[0];
            }
            
            if (toolCupboard == null)
            {
                SendReply(player, config.ErrorMessagePrefix + "No Tool Cupboard found nearby.");
                return;
            }
            
            // Filter blocks that need repair
            List<BuildingBlock> repairableBlocks = blocks.Where(block => 
                block.health < block.MaxHealth() && 
                !player.IsBuildingBlocked(block.WorldSpaceBounds())).ToList();
            
            if (repairableBlocks.Count == 0)
            {
                SendReply(player, config.ErrorMessagePrefix + "No blocks found that need repair.");
                return;
            }
            
            // Calculate total repair cost
            Dictionary<ItemDefinition, int> missingItems = new Dictionary<ItemDefinition, int>();
            Dictionary<int, int> totalCost = new Dictionary<int, int>();
            int success = 0;
            
            // Calculate costs for all blocks
            foreach (var block in repairableBlocks)
            {
                if (block == null || block.IsDestroyed)
                    continue;
                    
                // Calculate repair cost based on missing health percentage
                float healthFraction = 1f - (block.health / block.MaxHealth());
                
                // Get construction grade for the block
                var constructionGrade = block.blockDefinition.GetGrade(block.grade, 0);
                if (constructionGrade == null)
                    continue;
                    
                // Calculate repair cost based on grade
                foreach (var item in constructionGrade.CostToBuild())
                {
                    int amount = Mathf.CeilToInt(item.amount * healthFraction);
                    if (config.EnableCostMultiplier)
                        amount = Mathf.CeilToInt(amount * config.RepairCostMultiplier);
                        
                    if (!totalCost.ContainsKey(item.itemid))
                        totalCost[item.itemid] = 0;
                        
                    totalCost[item.itemid] += amount;
                }
            }
            
            // Check if TC has enough resources
            if (config.RequireResourcesForRepairs && !permission.UserHasPermission(player.UserIDString, "tcupgrader.nocost"))
            {
                foreach (var cost in totalCost)
                {
                    int tcAmount = GetAmountFromTC(toolCupboard, cost.Key);
                    int requiredAmount = cost.Value;
                    
                    if (tcAmount < requiredAmount)
                    {
                        ItemDefinition itemDef = ItemManager.FindItemDefinition(cost.Key);
                        if (!missingItems.ContainsKey(itemDef))
                            missingItems[itemDef] = 0;
                            
                        missingItems[itemDef] += (requiredAmount - tcAmount);
                    }
                }
                
                // If missing resources, show message and return
                if (missingItems.Count > 0)
                {
                    StringBuilder stringBuilder = new StringBuilder();
                    foreach (var entry in missingItems)
                    {
                        stringBuilder.AppendLine($"* <color=#FF1919>{entry.Key.displayName.translated}</color> x{entry.Value}");
                    }
                    string missingResources = stringBuilder.ToString();
                    
                    SendReply(player, config.ErrorMessagePrefix + $"Tool Cupboard is missing resources needed for repair: \n{missingResources}");
                    return;
                }
                
                // Take resources from TC
                foreach (var cost in totalCost)
                {
                    TakeFromTC(toolCupboard, cost.Key, cost.Value);
                }
            }
            
            // Repair all blocks
            foreach (var block in repairableBlocks)
            {
                if (block == null || block.IsDestroyed)
                    continue;
                    
                block.health = block.MaxHealth();
                block.SendNetworkUpdate();
                success++;
            }
            
            // Send result message to player
            if (success > 0)
            {
                SendReply(player, config.SuccessMessagePrefix + string.Format(config.RepairSuccessMessage, success));
            }
            else
            {
                SendReply(player, config.ErrorMessagePrefix + "No blocks were repaired.");
            }
            
            // Play sound if enabled
            if (config.EnableRepairSounds && success > 0)
            {
                Effect.server.Run("assets/bundled/prefabs/fx/build/repair.prefab", player.transform.position);
            }
        }

        // Helper method to get amount of an item from TC
        private int GetAmountFromTC(BuildingPrivlidge toolCupboard, int itemId)
        {
            if (toolCupboard == null || toolCupboard.inventory == null)
                return 0;
                
            int amount = 0;
            foreach (var item in toolCupboard.inventory.itemList)
            {
                if (item.info.itemid == itemId)
                    amount += item.amount;
            }
            
            return amount;
        }

        // Helper method to take items from TC
        private void TakeFromTC(BuildingPrivlidge toolCupboard, int itemId, int amount)
        {
            if (toolCupboard == null || toolCupboard.inventory == null || amount <= 0)
                return;
                
            int remaining = amount;
            
            // Find items of the required type
            List<Item> items = toolCupboard.inventory.itemList.Where(item => item.info.itemid == itemId).ToList();
            
            foreach (var item in items)
            {
                if (remaining <= 0)
                    break;
                    
                if (item.amount <= remaining)
                {
                    // Take the entire stack
                    remaining -= item.amount;
                    item.Remove();
                }
                else
                {
                    // Take part of the stack
                    item.amount -= remaining;
                    item.MarkDirty();
                    remaining = 0;
                }
            }
        }

        private string GetGradeName(BuildingGrade.Enum grade)
        {
            switch (grade)
            {
                case BuildingGrade.Enum.Wood:
                    return "Wood";
                case BuildingGrade.Enum.Stone:
                    return "Stone";
                case BuildingGrade.Enum.Metal:
                    return "Metal";
                case BuildingGrade.Enum.TopTier:
                    return "Armored";
                default:
                    return "Unknown";
            }
        }

        private void Unload()
        {
            foreach (var player in BasePlayer.activePlayerList)
            {
                DestroyUI(player);
            }
        }

        [ConsoleCommand("tcupgrade")]
        private void ConsoleCommandUpgrade(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;
            
            if (config.EnablePermission && !permission.UserHasPermission(player.UserIDString, PERMISSION_USE))
            {
                SendReply(player, config.ErrorMessagePrefix + config.NoPermissionMessage);
                return;
            }
            
            if (arg.Args == null || arg.Args.Length < 1)
            {
                SendReply(player, config.ErrorMessagePrefix + "Usage: tcupgrade <wood|stone|metal|armored>");
                return;
            }
            
            BuildingGrade.Enum grade;
            switch (arg.Args[0].ToLower())
            {
                case "0":
                case "wood":
                    grade = BuildingGrade.Enum.Wood;
                    break;
                case "1":
                case "stone":
                    grade = BuildingGrade.Enum.Stone;
                    break;
                case "2":
                case "metal":
                    grade = BuildingGrade.Enum.Metal;
                    break;
                case "3":
                case "armored":
                case "hqm":
                case "toptier":
                    grade = BuildingGrade.Enum.TopTier;
                    break;
                default:
                    SendReply(player, config.ErrorMessagePrefix + "Invalid grade. Use: wood, stone, metal, or armored");
                    return;
            }
            
            UpgradeNearbyBlocks(player, grade);
        }

        [ConsoleCommand("tcrepair")]
        private void ConsoleCommandRepair(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;
            
            if (config.EnablePermission && !permission.UserHasPermission(player.UserIDString, PERMISSION_USE))
            {
                SendReply(player, config.ErrorMessagePrefix + config.NoPermissionMessage);
                return;
            }
            
            RepairNearbyBlocks(player);
        }

        // Helper method to get the correct foundation prefab path based on type and grade
        private string GetCorrectFoundationPrefab(string originalPrefab, BuildingGrade.Enum grade)
        {
            // Extract the foundation type from the original prefab
            string foundationType = "foundation";
            if (originalPrefab.Contains("foundation.triangle"))
                foundationType = "foundation.triangle";
            else if (originalPrefab.Contains("foundation.steps"))
                foundationType = "foundation.steps";
            
            // Construct the new prefab path with the correct grade
            string gradeName = "";
            switch (grade)
            {
                case BuildingGrade.Enum.Wood:
                    gradeName = "wood";
                    break;
                case BuildingGrade.Enum.Stone:
                    gradeName = "stone";
                    break;
                case BuildingGrade.Enum.Metal:
                    gradeName = "metal";
                    break;
                case BuildingGrade.Enum.TopTier:
                    gradeName = "toptier";
                    break;
            }
            
            // The correct path format based on the original path
            if (originalPrefab.Contains("building core"))
            {
                return $"assets/prefabs/building core/{foundationType}/{foundationType}.{gradeName}.prefab";
            }
            else
            {
                return $"assets/prefabs/building/{foundationType}/{foundationType}.{gradeName}.prefab";
            }
        }
    }
}


















































