using System;
using System.Collections.Generic;
using System.Linq;
using Oxide.Core;
using Oxide.Core.Plugins;
using UnityEngine;
using Newtonsoft.Json;

namespace Oxide.Plugins
{
    [Info("Monument Music", "Frizzo420", "1.0.0")]
    [Description("Allows server owners to place boom boxes at monuments that play custom music")]
    class MonumentMusic : RustPlugin
    {
        #region Configuration
        
        private Configuration config;
        
        class Configuration
        {
            [JsonProperty("Monument Music Settings")]
            public Dictionary<string, MonumentMusicSettings> MonumentSettings = new Dictionary<string, MonumentMusicSettings>();
            
            [JsonProperty("Default Volume")]
            public float DefaultVolume = 1.0f;
            
            [JsonProperty("Default Range")]
            public float DefaultRange = 30f;
            
            [JsonProperty("Admin Permission")]
            public string AdminPermission = "monumentmusic.admin";
        }
        
        class MonumentMusicSettings
        {
            [JsonProperty("Music URL")]
            public string MusicUrl = "";
            
            [JsonProperty("Volume")]
            public float Volume = 1.0f;
            
            [JsonProperty("Range")]
            public float Range = 30f;
            
            [JsonProperty("Position")]
            public Vector3 Position = Vector3.zero;
            
            [JsonProperty("Rotation")]
            public Quaternion Rotation = Quaternion.identity;
            
            [JsonProperty("Enabled")]
            public bool Enabled = true;
        }
        
        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
            }
            catch
            {
                PrintError("Configuration file is corrupt! Loading default configuration...");
                LoadDefaultConfig();
            }
            SaveConfig();
        }
        
        protected override void LoadDefaultConfig()
        {
            config = new Configuration();
        }
        
        protected override void SaveConfig()
        {
            Config.WriteObject(config);
        }
        
        #endregion
        
        #region Data
        
        private Dictionary<string, BaseEntity> monumentBoomBoxes = new Dictionary<string, BaseEntity>();
        
        #endregion
        
        #region Hooks
        
        void OnServerInitialized()
        {
            // Register permission
            permission.RegisterPermission(config.AdminPermission, this);
            
            // Register commands
            cmd.AddChatCommand("mm", this, "CmdMonumentMusic");
            
            // Load monument boom boxes
            timer.Once(5f, () => {
                SpawnMonumentBoomBoxes();
            });
            
            Puts("Monument Music plugin initialized!");
        }
        
        void Unload()
        {
            // Destroy all boom boxes
            foreach (var boomBox in monumentBoomBoxes.Values)
            {
                if (boomBox != null && !boomBox.IsDestroyed)
                {
                    boomBox.Kill();
                }
            }
        }
        
        #endregion
        
        #region Commands
        
        void CmdMonumentMusic(BasePlayer player, string command, string[] args)
        {
            if (player == null || !permission.UserHasPermission(player.UserIDString, config.AdminPermission))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            if (args.Length == 0)
            {
                SendReply(player, "Monument Music Commands:");
                SendReply(player, "/mm list - List all monuments");
                SendReply(player, "/mm place <monument> - Place a boom box at your current position for a monument");
                SendReply(player, "/mm url <monument> <url> - Set music URL for a monument");
                SendReply(player, "/mm volume <monument> <0.0-1.0> - Set volume for a monument");
                SendReply(player, "/mm range <monument> <range> - Set range for a monument");
                SendReply(player, "/mm enable <monument> - Enable music for a monument");
                SendReply(player, "/mm disable <monument> - Disable music for a monument");
                SendReply(player, "/mm reload - Reload all boom boxes");
                return;
            }
            
            string subCommand = args[0].ToLower();
            
            if (subCommand == "list")
            {
                // List all monuments on the server
                List<string> monumentNames = new List<string>();
                foreach (var monument in TerrainMeta.Path.Monuments)
                {
                    if (monument != null)
                    {
                        monumentNames.Add(monument.name.ToLower());
                    }
                }
                
                SendReply(player, $"Found {monumentNames.Count} monuments on the map:");
                foreach (var name in monumentNames)
                {
                    bool hasMusic = config.MonumentSettings.ContainsKey(name) && !string.IsNullOrEmpty(config.MonumentSettings[name].MusicUrl);
                    bool isEnabled = hasMusic && config.MonumentSettings[name].Enabled;
                    SendReply(player, $"- {name} {(hasMusic ? (isEnabled ? "[ENABLED]" : "[DISABLED]") : "[NO MUSIC]")}");
                }
                return;
            }
            
            if (subCommand == "place" && args.Length > 1)
            {
                string monumentName = args[1].ToLower();
                
                // Check if monument exists
                bool monumentExists = false;
                foreach (var monument in TerrainMeta.Path.Monuments)
                {
                    if (monument != null && monument.name.ToLower() == monumentName)
                    {
                        monumentExists = true;
                        break;
                    }
                }
                
                if (!monumentExists)
                {
                    SendReply(player, $"Monument '{monumentName}' not found. Use /mm list to see available monuments.");
                    return;
                }
                
                // Create or update monument settings
                if (!config.MonumentSettings.ContainsKey(monumentName))
                {
                    config.MonumentSettings[monumentName] = new MonumentMusicSettings();
                }
                
                // Update position and rotation
                config.MonumentSettings[monumentName].Position = player.transform.position;
                config.MonumentSettings[monumentName].Rotation = player.transform.rotation;
                
                // Save config
                SaveConfig();
                
                // Respawn boom box
                RespawnBoomBox(monumentName);
                
                SendReply(player, $"Boom box placed at your position for monument '{monumentName}'.");
                return;
            }
            
            if (subCommand == "url" && args.Length > 2)
            {
                string monumentName = args[1].ToLower();
                string url = args[2];
                
                if (!config.MonumentSettings.ContainsKey(monumentName))
                {
                    SendReply(player, $"Monument '{monumentName}' has no boom box placed. Use /mm place first.");
                    return;
                }
                
                // Update URL
                config.MonumentSettings[monumentName].MusicUrl = url;
                
                // Save config
                SaveConfig();
                
                // Update boom box
                UpdateBoomBoxSettings(monumentName);
                
                SendReply(player, $"Music URL for monument '{monumentName}' set to: {url}");
                return;
            }
            
            if (subCommand == "volume" && args.Length > 2)
            {
                string monumentName = args[1].ToLower();
                
                if (!config.MonumentSettings.ContainsKey(monumentName))
                {
                    SendReply(player, $"Monument '{monumentName}' has no boom box placed. Use /mm place first.");
                    return;
                }
                
                float volume;
                if (!float.TryParse(args[2], out volume))
                {
                    SendReply(player, "Volume must be a number between 0.0 and 1.0");
                    return;
                }
                
                volume = Mathf.Clamp(volume, 0f, 1f);
                
                // Update volume
                config.MonumentSettings[monumentName].Volume = volume;
                
                // Save config
                SaveConfig();
                
                // Update boom box
                UpdateBoomBoxSettings(monumentName);
                
                SendReply(player, $"Volume for monument '{monumentName}' set to: {volume}");
                return;
            }
            
            if (subCommand == "range" && args.Length > 2)
            {
                string monumentName = args[1].ToLower();
                
                if (!config.MonumentSettings.ContainsKey(monumentName))
                {
                    SendReply(player, $"Monument '{monumentName}' has no boom box placed. Use /mm place first.");
                    return;
                }
                
                float range;
                if (!float.TryParse(args[2], out range))
                {
                    SendReply(player, "Range must be a number");
                    return;
                }
                
                range = Mathf.Max(1f, range);
                
                // Update range
                config.MonumentSettings[monumentName].Range = range;
                
                // Save config
                SaveConfig();
                
                // Update boom box
                UpdateBoomBoxSettings(monumentName);
                
                SendReply(player, $"Range for monument '{monumentName}' set to: {range}");
                return;
            }
            
            if (subCommand == "enable" && args.Length > 1)
            {
                string monumentName = args[1].ToLower();
                
                if (!config.MonumentSettings.ContainsKey(monumentName))
                {
                    SendReply(player, $"Monument '{monumentName}' has no boom box placed. Use /mm place first.");
                    return;
                }
                
                // Enable music
                config.MonumentSettings[monumentName].Enabled = true;
                
                // Save config
                SaveConfig();
                
                // Respawn boom box
                RespawnBoomBox(monumentName);
                
                SendReply(player, $"Music for monument '{monumentName}' enabled.");
                return;
            }
            
            if (subCommand == "disable" && args.Length > 1)
            {
                string monumentName = args[1].ToLower();
                
                if (!config.MonumentSettings.ContainsKey(monumentName))
                {
                    SendReply(player, $"Monument '{monumentName}' has no boom box placed. Use /mm place first.");
                    return;
                }
                
                // Disable music
                config.MonumentSettings[monumentName].Enabled = false;
                
                // Save config
                SaveConfig();
                
                // Remove boom box
                if (monumentBoomBoxes.ContainsKey(monumentName))
                {
                    var boomBox = monumentBoomBoxes[monumentName];
                    if (boomBox != null && !boomBox.IsDestroyed)
                    {
                        boomBox.Kill();
                    }
                    monumentBoomBoxes.Remove(monumentName);
                }
                
                SendReply(player, $"Music for monument '{monumentName}' disabled.");
                return;
            }
            
            if (subCommand == "reload")
            {
                // Reload all boom boxes
                SpawnMonumentBoomBoxes();
                SendReply(player, "All monument boom boxes reloaded.");
                return;
            }
            
            // Unknown command
            SendReply(player, "Unknown command. Use /mm for help.");
        }
        
        #endregion
        
        #region Methods
        
        private void SpawnMonumentBoomBoxes()
        {
            // Destroy existing boom boxes
            foreach (var boomBox in monumentBoomBoxes.Values)
            {
                if (boomBox != null && !boomBox.IsDestroyed)
                {
                    boomBox.Kill();
                }
            }
            monumentBoomBoxes.Clear();
            
            // Spawn new boom boxes
            foreach (var entry in config.MonumentSettings)
            {
                string monumentName = entry.Key;
                var settings = entry.Value;
                
                if (settings.Enabled && !string.IsNullOrEmpty(settings.MusicUrl) && settings.Position != Vector3.zero)
                {
                    SpawnBoomBox(monumentName, settings);
                }
            }
        }
        
        private void RespawnBoomBox(string monumentName)
        {
            // Remove existing boom box
            if (monumentBoomBoxes.ContainsKey(monumentName))
            {
                var boomBox = monumentBoomBoxes[monumentName];
                if (boomBox != null && !boomBox.IsDestroyed)
                {
                    boomBox.Kill();
                }
                monumentBoomBoxes.Remove(monumentName);
            }
            
            // Spawn new boom box if enabled
            if (config.MonumentSettings.ContainsKey(monumentName))
            {
                var settings = config.MonumentSettings[monumentName];
                if (settings.Enabled && !string.IsNullOrEmpty(settings.MusicUrl) && settings.Position != Vector3.zero)
                {
                    SpawnBoomBox(monumentName, settings);
                }
            }
        }
        
        private void SpawnBoomBox(string monumentName, MonumentMusicSettings settings)
        {
            // Create boom box
            var boomBox = GameManager.server.CreateEntity("assets/prefabs/deployable/boombox/boombox.deployed.prefab", 
                settings.Position, settings.Rotation) as BaseEntity;
            
            if (boomBox == null)
            {
                PrintError($"Failed to create boom box for monument '{monumentName}'");
                return;
            }
            
            // Spawn boom box
            boomBox.Spawn();
            
            // Set owner to server
            boomBox.OwnerID = 0;
            
            // Make it indestructible
            boomBox.SetFlag(BaseEntity.Flags.Reserved8, true);
            
            // Configure boom box
            ConfigureBoomBox(boomBox, settings);
            
            // Add to dictionary
            monumentBoomBoxes[monumentName] = boomBox;
            
            Puts($"Spawned boom box for monument '{monumentName}' at {settings.Position}");
        }
        
        private void ConfigureBoomBox(BaseEntity entity, MonumentMusicSettings settings)
        {
            if (entity == null || entity.IsDestroyed) return;
            
            // Cast to BoomBox
            var boomBox = entity as DeployableBoomBox;
            if (boomBox == null) return;
            
            // Set URL
            boomBox.ServerSetUrl(settings.MusicUrl);
            
            // Set volume
            boomBox.musicVolume = settings.Volume;
            
            // Set range (using reflection as there's no direct API)
            var radioEntity = boomBox.GetComponent<IOEntity>();
            if (radioEntity != null)
            {
                var broadcastField = radioEntity.GetType().GetField("broadcastRadius", 
                    System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
                
                if (broadcastField != null)
                {
                    broadcastField.SetValue(radioEntity, settings.Range);
                }
            }
            
            // Start playing
            boomBox.ServerTogglePlay(true);
        }
        
        private void UpdateBoomBoxSettings(string monumentName)
        {
            if (!monumentBoomBoxes.ContainsKey(monumentName) || !config.MonumentSettings.ContainsKey(monumentName))
                return;
            
            var boomBox = monumentBoomBoxes[monumentName];
            var settings = config.MonumentSettings[monumentName];
            
            if (boomBox != null && !boomBox.IsDestroyed)
            {
                ConfigureBoomBox(boomBox, settings);
            }
        }
        
        #endregion
    }
}
