using System;
using System.Collections.Generic;
using Oxide.Core;
using Oxide.Core.Plugins;
using UnityEngine;
using Newtonsoft.Json;

namespace Oxide.Plugins
{
    [Info("Drug Business", "YourName", "1.0.0")]
    [Description("Allows players to grow, process, and sell drugs for profit")]
    class DrugBusiness : RustPlugin
    {
        #region Configuration
        
        private Configuration config;
        
        class Configuration
        {
            [JsonProperty("Drug Plants")]
            public Dictionary<string, DrugPlantConfig> DrugPlants = new Dictionary<string, DrugPlantConfig>
            {
                ["cannabis"] = new DrugPlantConfig
                {
                    GrowthTime = 600f,
                    YieldAmount = 5,
                    SeedCost = 50,
                    SellPrice = 25
                }
            };
            
            [JsonProperty("Processing Stations")]
            public Dictionary<string, ProcessingConfig> ProcessingStations = new Dictionary<string, ProcessingConfig>
            {
                ["lab"] = new ProcessingConfig
                {
                    PrefabPath = "assets/prefabs/deployable/furnace/furnace.prefab",
                    ProcessTime = 120f,
                    InputItems = new Dictionary<string, int> { ["cannabis"] = 5 },
                    OutputItems = new Dictionary<string, int> { ["processed_cannabis"] = 1 }
                }
            };
            
            [JsonProperty("Dealer NPCs")]
            public List<DealerConfig> Dealers = new List<DealerConfig>
            {
                new DealerConfig
                {
                    Position = new Vector3(0, 0, 0),
                    Rotation = new Vector3(0, 0, 0),
                    BuyItems = new Dictionary<string, int> { ["processed_cannabis"] = 100 }
                }
            };
            
            [JsonProperty("Police Raid Chance")]
            public float PoliceRaidChance = 0.05f;
            
            [JsonProperty("Raid Response Time (seconds)")]
            public float RaidResponseTime = 300f;
        }
        
        class DrugPlantConfig
        {
            [JsonProperty("Growth Time (seconds)")]
            public float GrowthTime;
            
            [JsonProperty("Yield Amount")]
            public int YieldAmount;
            
            [JsonProperty("Seed Cost")]
            public int SeedCost;
            
            [JsonProperty("Sell Price")]
            public int SellPrice;
        }
        
        class ProcessingConfig
        {
            [JsonProperty("Prefab Path")]
            public string PrefabPath;
            
            [JsonProperty("Process Time (seconds)")]
            public float ProcessTime;
            
            [JsonProperty("Input Items (item:amount)")]
            public Dictionary<string, int> InputItems;
            
            [JsonProperty("Output Items (item:amount)")]
            public Dictionary<string, int> OutputItems;
        }
        
        class DealerConfig
        {
            [JsonProperty("Position")]
            public Vector3 Position;
            
            [JsonProperty("Rotation")]
            public Vector3 Rotation;
            
            [JsonProperty("Buy Items (item:price)")]
            public Dictionary<string, int> BuyItems;
        }
        
        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
            }
            catch
            {
                PrintError("Configuration file is corrupt! Loading default configuration...");
                LoadDefaultConfig();
            }
            SaveConfig();
        }
        
        protected override void LoadDefaultConfig()
        {
            config = new Configuration();
        }
        
        protected override void SaveConfig()
        {
            Config.WriteObject(config);
        }
        
        #endregion
        
        #region Data
        
        private StoredData storedData;
        
        class StoredData
        {
            public Dictionary<ulong, PlayerData> Players = new Dictionary<ulong, PlayerData>();
            public List<DrugPlant> Plants = new List<DrugPlant>();
            public List<ProcessingStation> Stations = new List<ProcessingStation>();
        }
        
        class PlayerData
        {
            public int Reputation = 0;
            public int DrugsMade = 0;
            public int DrugsProcessed = 0;
            public int DrugsSold = 0;
            public int MoneyEarned = 0;
        }
        
        class DrugPlant
        {
            public string Type;
            public Vector3 Position;
            public ulong OwnerID;
            public float PlantTime;
            public float GrowthProgress;
        }
        
        class ProcessingStation
        {
            public string Type;
            public Vector3 Position;
            public ulong OwnerID;
            public Dictionary<string, int> Contents = new Dictionary<string, int>();
            public float ProcessStartTime;
            public float ProcessEndTime;
        }
        
        private void LoadData()
        {
            storedData = Interface.Oxide.DataFileSystem.ReadObject<StoredData>("DrugBusiness");
            if (storedData == null) storedData = new StoredData();
        }
        
        private void SaveData()
        {
            Interface.Oxide.DataFileSystem.WriteObject("DrugBusiness", storedData);
        }
        
        #endregion
        
        #region Hooks
        
        [PluginReference] private Plugin Economics;
        
        void OnServerInitialized()
        {
            // Register permissions
            permission.RegisterPermission("drugbusiness.use", this);
            permission.RegisterPermission("drugbusiness.admin", this);
            
            // Register commands
            cmd.AddChatCommand("drugs", this, "CmdDrugs");
            cmd.AddChatCommand("plant", this, "CmdPlant");
            cmd.AddChatCommand("process", this, "CmdProcess");
            cmd.AddChatCommand("dealer", this, "CmdDealer");
            
            // Load data
            LoadData();
            
            // Spawn dealers
            SpawnDealers();
            
            Puts("Drug Business plugin initialized!");
        }
        
        void Unload()
        {
            // Save data
            SaveData();
            
            // Clean up entities
            // ...
        }
        
        #endregion
        
        #region Commands
        
        private void CmdDrugs(BasePlayer player, string command, string[] args)
        {
            // Show drug business menu
            // ...
        }
        
        private void CmdPlant(BasePlayer player, string command, string[] args)
        {
            // Plant a drug plant
            // ...
        }
        
        private void CmdProcess(BasePlayer player, string command, string[] args)
        {
            // Process drugs
            // ...
        }
        
        private void CmdDealer(BasePlayer player, string command, string[] args)
        {
            // Find nearest dealer
            // ...
        }
        
        #endregion
        
        #region Core Functionality
        
        private void SpawnDealers()
        {
            // Spawn dealer NPCs at configured locations
            // ...
        }
        
        private void UpdatePlants()
        {
            // Update growth of all plants
            // ...
        }
        
        private void CheckForPoliceRaids()
        {
            // Random chance to trigger police raids on players with high reputation
            // ...
        }
        
        private void SellDrugs(BasePlayer player, string drugType, int amount)
        {
            // Handle drug selling to dealers
            // ...
            
            // Use Economics plugin if available
            if (Economics != null)
            {
                int price = config.DrugPlants[drugType].SellPrice * amount;
                Economics.Call("Deposit", player.userID, (double)price);
                SendReply(player, $"You sold {amount}x {drugType} for ${price}");
            }
        }
        
        #endregion
    }
}