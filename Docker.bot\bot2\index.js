const { Client, GatewayIntentBits } = require('discord.js');
const client = new Client({ 
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent
  ] 
});

// Bot configuration
const BOT_NAME = 'Rust Server 2 Bot';
const SERVER_NAME = process.env.SERVER_NAME || 'Rust Server 2';

client.once('ready', () => {
  console.log(`${BOT_NAME} is online!`);
});

client.on('messageCreate', message => {
  if (message.author.bot) return;
  
  if (message.content.startsWith('!status')) {
    message.reply(`${SERVER_NAME} is running!`);
  }
});

client.login(process.env.DISCORD_TOKEN);