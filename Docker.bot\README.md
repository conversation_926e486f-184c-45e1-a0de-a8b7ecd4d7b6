# Rust Server Discord Bots

This project contains three Discord bots for monitoring and interacting with Rust game servers.

## Installation Instructions

### Linux Installation

#### Prerequisites
- Linux server
- Docker and <PERSON><PERSON> Compose installed

#### Installation Steps

1. **Clone or upload the repository to your server**
   ```bash
   git clone https://github.com/yourusername/rust-discord-bots.git
   cd rust-discord-bots
   ```

2. **Configure each bot**
   - Edit the `.env` file in each bot directory (bot1, bot2, bot3)
   - Add your Discord bot tokens:
     ```
     DISCORD_TOKEN=your_bot_token_here
     SERVER_NAME=Your Server Name
     ```

3. **Start the bots**
   ```bash
   docker-compose up -d
   ```

4. **Check logs**
   ```bash
   docker-compose logs -f
   ```

### Windows Installation

#### Prerequisites
1. **Install Docker Desktop for Windows**
   - Download from [<PERSON><PERSON>'s official website](https://www.docker.com/products/docker-desktop)
   - Follow the installation instructions
   - Make sure Docker Desktop is running before proceeding

2. **Install Git (Optional)**
   - Download from [Git's official website](https://git-scm.com/download/win)
   - This is optional if you download the project as a ZIP file

#### Installation Steps

1. **Get the code**
   - **Option 1**: Clone with Git
     ```
     git clone https://github.com/yourusername/rust-discord-bots.git
     cd rust-discord-bots
     ```
   - **Option 2**: Download ZIP and extract to a folder (e.g., `C:\RustBots`)
     ```
     cd C:\RustBots
     ```

2. **Configure each bot**
   - Edit the `.env` file in each bot directory (bot1, bot2, bot3)
   - Add your Discord bot tokens:
     ```
     DISCORD_TOKEN=your_bot_token_here
     SERVER_NAME=Your Server Name
     ```

3. **Start Docker Desktop**
   - Make sure it's running by checking the Docker icon in your system tray

4. **Start the bots**
   - Open Command Prompt or PowerShell as Administrator
   - Navigate to your project folder
   - Run:
     ```
     docker-compose up -d
     ```

5. **Check logs**
   ```
   docker-compose logs -f
   ```

#### Troubleshooting Windows-Specific Issues

1. **Docker Desktop not starting**
   - Make sure Hyper-V is enabled in Windows features
   - For Windows Home, make sure WSL2 is installed and configured

2. **Permission issues**
   - Run Command Prompt or PowerShell as Administrator

3. **Path too long errors**
   - Use shorter paths or enable long path support in Windows

4. **Docker volume mounting issues**
   - Use forward slashes (/) instead of backslashes (\\) in your docker-compose.yml file
   - Make sure the paths are correctly specified

## Bot Commands

### General Commands

| Command | Description |
|---------|-------------|
| `!status` | Shows if the Rust server is running |
| `!help` | Displays a list of available commands |
| `!info` | Shows server information |

### Admin Commands

| Command | Description |
|---------|-------------|
| `!restart` | Restarts the Rust server (admin only) |
| `!wipe` | Wipes the server (admin only) |
| `!broadcast <message>` | Broadcasts a message to all players |

### Player Commands

| Command | Description |
|---------|-------------|
| `!players` | Shows current online players |
| `!time` | Shows in-game time |
| `!map` | Displays server map information |

## Managing the Bots

### View logs
```bash
# All bots
docker-compose logs -f

# Specific bot
docker-compose logs -f bot1
```

### Restart bots
```bash
# All bots
docker-compose restart

# Specific bot
docker-compose restart bot1
```

### Stop bots
```bash
docker-compose down
```

### Update bots
```bash
git pull
docker-compose down
docker-compose up -d --build
```

## Troubleshooting

- **Bot not connecting to Discord**: Check your token in the `.env` file
- **Command not working**: Ensure the bot has proper permissions in your Discord server
- **Docker issues**: Make sure Docker and Docker Compose are installed and running

## Support

For support, please join our Discord server: [discord.gg/yourserver](https://discord.gg/yourserver)

## License

This project is licensed under the MIT License - see the LICENSE file for details.
