using System.Collections.Generic;
using UnityEngine;
using Oxide.Core;
using System;
using System.Linq;
using Oxide.Game.Rust.Cui;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Vehicle Turn Signals", "YourName", "1.0.0")]
    [Description("Adds turn signals to vehicles")]
    public class VehicleSignals : RustPlugin
    {
        private Dictionary<BaseVehicle, SignalState> vehicleSignals = new Dictionary<BaseVehicle, SignalState>();
        private Dictionary<BaseVehicle, List<GameObject>> signalLights = new Dictionary<BaseVehicle, List<GameObject>>();
        
        private const string LeftSignalPrefab = "assets/prefabs/deployable/playerioents/lights/flasherlight/electric.flasherlight.deployed.prefab";
        private const string RightSignalPrefab = "assets/prefabs/deployable/playerioents/lights/flasherlight/electric.flasherlight.deployed.prefab";
        private const string GUIPanel = "VehicleSignals_KeybindGUI";
        
        private class SignalState
        {
            public bool LeftSignal { get; set; }
            public bool RightSignal { get; set; }
            public bool Hazards { get; set; }
            public Timer BlinkTimer { get; set; }
            public bool LightState { get; set; }
        }

        #region Configuration
        private Configuration config;
        public class Configuration
        {
            public string LeftSignalKey = "LeftArrow";
            public string RightSignalKey = "RightArrow";
            public string HazardsKey = "H";
            public string ButtonColor = "0.7 0.7 0.7 0.9";
            public string PanelColor = "0.1 0.1 0.1 0.9";
            public string TextColor = "1 1 1 0.9";
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
            }
            catch
            {
                LoadDefaultConfig();
            }
            SaveConfig();
        }

        protected override void LoadDefaultConfig()
        {
            config = new Configuration();
        }

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        void OnServerInitialized()
        {
            cmd.AddChatCommand("leftsignal", this, "CmdLeftSignal");
            cmd.AddChatCommand("rightsignal", this, "CmdRightSignal");
            cmd.AddChatCommand("hazards", this, "CmdHazards");
            cmd.AddChatCommand("signalconfig", this, "CmdSignalConfig");
        }

        void Unload()
        {
            foreach (var vehicle in vehicleSignals.Keys.ToList())
            {
                CleanupVehicle(vehicle);
            }
            vehicleSignals.Clear();
            signalLights.Clear();
            foreach (var player in BasePlayer.activePlayerList)
            {
                DestroyGUI(player);
            }
        }

        private void CleanupVehicle(BaseVehicle vehicle)
        {
            if (vehicleSignals.ContainsKey(vehicle))
            {
                vehicleSignals[vehicle].BlinkTimer?.Destroy();
                
                if (signalLights.ContainsKey(vehicle))
                {
                    foreach (var light in signalLights[vehicle])
                    {
                        if (light != null)
                            UnityEngine.Object.Destroy(light);
                    }
                    signalLights.Remove(vehicle);
                }
                
                vehicleSignals.Remove(vehicle);
            }
        }

        void OnEntityMounted(BaseMountable mountable, BasePlayer player)
        {
            var vehicle = mountable.GetComponentInParent<BaseVehicle>();
            if (vehicle == null || !IsValidVehicle(vehicle)) return;

            if (!vehicleSignals.ContainsKey(vehicle))
            {
                InitializeVehicleSignals(vehicle);
            }
        }

        void OnEntityDismounted(BaseMountable mountable, BasePlayer player)
        {
            var vehicle = mountable.GetComponentInParent<BaseVehicle>();
            if (vehicle == null) return;

            bool noPlayersInVehicle = true;
            foreach (var mountPoint in vehicle.mountPoints)
            {
                if (mountPoint.mountable?.GetMounted() != null)
                {
                    noPlayersInVehicle = false;
                    break;
                }
            }

            if (noPlayersInVehicle)
            {
                TurnOffAllSignals(vehicle);
            }
        }

        private bool IsValidVehicle(BaseVehicle vehicle)
        {
            return vehicle is ModularCar || vehicle is BasicCar;
        }

        private void InitializeVehicleSignals(BaseVehicle vehicle)
        {
            vehicleSignals[vehicle] = new SignalState
            {
                LeftSignal = false,
                RightSignal = false,
                Hazards = false,
                LightState = false
            };

            CreateSignalLights(vehicle);
        }

        private void CreateSignalLights(BaseVehicle vehicle)
        {
            if (signalLights.ContainsKey(vehicle)) return;

            var lights = new List<GameObject>();
            
            var leftFront = CreateLight(vehicle, new Vector3(-0.5f, 0.5f, 1f));
            var leftRear = CreateLight(vehicle, new Vector3(-0.5f, 0.5f, -1f));
            var rightFront = CreateLight(vehicle, new Vector3(0.5f, 0.5f, 1f));
            var rightRear = CreateLight(vehicle, new Vector3(0.5f, 0.5f, -1f));

            lights.AddRange(new[] { leftFront, leftRear, rightFront, rightRear });
            signalLights[vehicle] = lights;
        }

        private GameObject CreateLight(BaseVehicle vehicle, Vector3 localPosition)
        {
            var light = GameManager.server.CreateEntity(LeftSignalPrefab) as IOEntity;
            if (light == null) return null;

            light.transform.position = vehicle.transform.TransformPoint(localPosition);
            light.transform.SetParent(vehicle.transform);
            light.SetFlag(BaseEntity.Flags.On, false);
            light.Spawn();

            return light.gameObject;
        }

        private void ToggleSignalLights(BaseVehicle vehicle, bool leftSide, bool rightSide)
        {
            if (!signalLights.ContainsKey(vehicle)) return;

            var lights = signalLights[vehicle];
            var state = vehicleSignals[vehicle].LightState;

            if (leftSide)
            {
                ToggleLight(lights[0], state);
                ToggleLight(lights[1], state);
            }

            if (rightSide)
            {
                ToggleLight(lights[2], state);
                ToggleLight(lights[3], state);
            }
        }

        private void ToggleLight(GameObject lightObj, bool state)
        {
            var light = lightObj?.GetComponent<IOEntity>();
            if (light != null)
            {
                light.SetFlag(BaseEntity.Flags.On, state);
            }
        }

        private void StartBlinking(BaseVehicle vehicle)
        {
            var state = vehicleSignals[vehicle];
            state.BlinkTimer?.Destroy();

            state.BlinkTimer = timer.Every(0.5f, () =>
            {
                if (vehicle == null || vehicle.IsDestroyed)
                {
                    state.BlinkTimer?.Destroy();
                    return;
                }

                state.LightState = !state.LightState;
                ToggleSignalLights(vehicle, 
                    state.LeftSignal || state.Hazards,
                    state.RightSignal || state.Hazards);
            });
        }

        private void TurnOffAllSignals(BaseVehicle vehicle)
        {
            if (!vehicleSignals.ContainsKey(vehicle)) return;

            var state = vehicleSignals[vehicle];
            state.LeftSignal = false;
            state.RightSignal = false;
            state.Hazards = false;
            state.BlinkTimer?.Destroy();

            ToggleSignalLights(vehicle, false, false);
        }

        void CmdLeftSignal(BasePlayer player, string command, string[] args)
        {
            var vehicle = player.GetMountedVehicle();
            if (vehicle == null || !IsValidVehicle(vehicle))
            {
                SendReply(player, "You must be in a valid vehicle to use turn signals.");
                return;
            }

            if (!vehicleSignals.ContainsKey(vehicle))
                InitializeVehicleSignals(vehicle);

            var state = vehicleSignals[vehicle];
            state.LeftSignal = !state.LeftSignal;
            state.RightSignal = false;
            state.Hazards = false;

            if (state.LeftSignal)
            {
                StartBlinking(vehicle);
                SendReply(player, "Left turn signal activated.");
            }
            else
            {
                TurnOffAllSignals(vehicle);
                SendReply(player, "Turn signals deactivated.");
            }
        }

        void CmdRightSignal(BasePlayer player, string command, string[] args)
        {
            var vehicle = player.GetMountedVehicle();
            if (vehicle == null || !IsValidVehicle(vehicle))
            {
                SendReply(player, "You must be in a valid vehicle to use turn signals.");
                return;
            }

            if (!vehicleSignals.ContainsKey(vehicle))
                InitializeVehicleSignals(vehicle);

            var state = vehicleSignals[vehicle];
            state.RightSignal = !state.RightSignal;
            state.LeftSignal = false;
            state.Hazards = false;

            if (state.RightSignal)
            {
                StartBlinking(vehicle);
                SendReply(player, "Right turn signal activated.");
            }
            else
            {
                TurnOffAllSignals(vehicle);
                SendReply(player, "Turn signals deactivated.");
            }
        }

        void CmdHazards(BasePlayer player, string command, string[] args)
        {
            var vehicle = player.GetMountedVehicle();
            if (vehicle == null || !IsValidVehicle(vehicle))
            {
                SendReply(player, "You must be in a valid vehicle to use hazard lights.");
                return;
            }

            if (!vehicleSignals.ContainsKey(vehicle))
                InitializeVehicleSignals(vehicle);

            var state = vehicleSignals[vehicle];
            state.Hazards = !state.Hazards;
            state.LeftSignal = false;
            state.RightSignal = false;

            if (state.Hazards)
            {
                StartBlinking(vehicle);
                SendReply(player, "Hazard lights activated.");
            }
            else
            {
                TurnOffAllSignals(vehicle);
                SendReply(player, "Hazard lights deactivated.");
            }
        }

        void OnEntityKill(BaseNetworkable entity)
        {
            var vehicle = entity as BaseVehicle;
            if (vehicle != null)
            {
                CleanupVehicle(vehicle);
            }
        }

        #region GUI Methods
        void CmdSignalConfig(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.userID.ToString(), "vehiclesignals.use"))
            {
                SendReply(player, "You don't have permission to configure signals!");
                return;
            }

            ShowGUI(player);
        }

        private void ShowGUI(BasePlayer player)
        {
            DestroyGUI(player);

            var elements = new CuiElementContainer();

            // Main panel
            elements.Add(new CuiPanel
            {
                Image = { Color = config.PanelColor },
                RectTransform = { AnchorMin = "0.3 0.3", AnchorMax = "0.7 0.7" },
                CursorEnabled = true
            }, "Overlay", GUIPanel);

            // Title
            elements.Add(new CuiLabel
            {
                Text = { Text = "Signal Configuration", Color = config.TextColor, FontSize = 20, Align = TextAnchor.MiddleCenter },
                RectTransform = { AnchorMin = "0 0.85", AnchorMax = "1 0.95" }
            }, GUIPanel);

            // Left Signal Button
            AddKeyBindButton(elements, "Left Signal", config.LeftSignalKey, 0.7f, "left");

            // Right Signal Button
            AddKeyBindButton(elements, "Right Signal", config.RightSignalKey, 0.5f, "right");

            // Hazards Button
            AddKeyBindButton(elements, "Hazards", config.HazardsKey, 0.3f, "hazards");

            // Close button
            elements.Add(new CuiButton
            {
                Button = { Color = "0.8 0.2 0.2 0.9", Command = "vehiclesignals.closegui" },
                RectTransform = { AnchorMin = "0.9 0.9", AnchorMax = "0.98 0.98" },
                Text = { Text = "X", Color = config.TextColor, FontSize = 14, Align = TextAnchor.MiddleCenter }
            }, GUIPanel);

            CuiHelper.AddUi(player, elements);
        }

        private void AddKeyBindButton(CuiElementContainer elements, string label, string currentKey, float yPos, string signalType)
        {
            elements.Add(new CuiLabel
            {
                Text = { Text = label, Color = config.TextColor, FontSize = 14, Align = TextAnchor.MiddleLeft },
                RectTransform = { AnchorMin = $"0.1 {yPos}", AnchorMax = $"0.5 {yPos + 0.1}" }
            }, GUIPanel);

            elements.Add(new CuiButton
            {
                Button = { Color = config.ButtonColor, Command = $"vehiclesignals.setkey {signalType}" },
                RectTransform = { AnchorMin = $"0.55 {yPos}", AnchorMax = $"0.9 {yPos + 0.1}" },
                Text = { Text = currentKey, Color = config.TextColor, FontSize = 14, Align = TextAnchor.MiddleCenter }
            }, GUIPanel);
        }

        private void DestroyGUI(BasePlayer player)
        {
            CuiHelper.DestroyUi(player, GUIPanel);
        }
        #endregion

        #region Console Commands
        [ConsoleCommand("vehiclesignals.closegui")]
        private void CloseGUICommand(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;
            DestroyGUI(player);
        }

        [ConsoleCommand("vehiclesignals.setkey")]
        private void SetKeyCommand(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;

            string signalType = arg.GetString(0);
            string newKey = arg.GetString(1);

            switch (signalType.ToLower())
            {
                case "left":
                    config.LeftSignalKey = newKey;
                    break;
                case "right":
                    config.RightSignalKey = newKey;
                    break;
                case "hazards":
                    config.HazardsKey = newKey;
                    break;
            }

            SaveConfig();
            ShowGUI(player); // Refresh the GUI
            SendReply(player, $"Key binding updated for {signalType} signal!");
        }
        #endregion

        void OnPlayerInput(BasePlayer player, InputState input)
        {
            var vehicle = player.GetMountedVehicle();
            if (vehicle == null || !IsValidVehicle(vehicle)) return;

            // Check for key presses
            if (input.current.buttons != 0)
            {
                // Convert the configured keys to KeyCode enum values
                KeyCode leftKey = (KeyCode)System.Enum.Parse(typeof(KeyCode), config.LeftSignalKey);
                KeyCode rightKey = (KeyCode)System.Enum.Parse(typeof(KeyCode), config.RightSignalKey);
                KeyCode hazardsKey = (KeyCode)System.Enum.Parse(typeof(KeyCode), config.HazardsKey);

                // Check each configured key
                if (input.WasJustPressed(leftKey))
                    CmdLeftSignal(player, "", new string[0]);
                else if (input.WasJustPressed(rightKey))
                    CmdRightSignal(player, "", new string[0]);
                else if (input.WasJustPressed(hazardsKey))
                    CmdHazards(player, "", new string[0]);
            }
        }
    }
}
