const { Client, GatewayIntentBits } = require('discord.js');
const config = require('./CONFIGS/config.json');

const client = new Client({
  intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildMessages]
});

client.once('ready', async () => {
  console.log(`Logged in as ${client.user.tag}`);
  
  // List all servers the bot is in
  console.log('\nServers:');
  client.guilds.cache.forEach(guild => {
    console.log(`- ${guild.name} (ID: ${guild.id})`);
  });
  
  // List all channels the bot can see
  console.log('\nChannels:');
  client.guilds.cache.forEach(guild => {
    console.log(`\nChannels in ${guild.name}:`);
    guild.channels.cache
      .filter(channel => channel.type === 0) // 0 is text channel
      .forEach(channel => {
        console.log(`- #${channel.name} (ID: ${channel.id})`);
      });
  });
  
  process.exit(0);
});

client.login(config.GLOBAL_POP_BOT.BOT_TOKEN);