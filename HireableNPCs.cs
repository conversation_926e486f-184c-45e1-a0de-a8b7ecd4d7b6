using System;
using System.Collections.Generic;
using System.Linq;
using Oxide.Core;
using Oxide.Core.Plugins;
using UnityEngine;
using Newtonsoft.Json;

namespace Oxide.Plugins
{
    [Info("Hireable NPCs", "YourName", "1.0.0")]
    [Description("Adds hireable AI NPCs that follow, defend, gather resources, and have skill trees")]
    class HireableNPCs : RustPlugin
    {
        #region Fields
        private Configuration config;
        private Dictionary<ulong, List<HireableNPC>> playerNPCs = new Dictionary<ulong, List<HireableNPC>>();
        private Dictionary<NetworkableId, HireableNPC> activeNPCs = new Dictionary<NetworkableId, HireableNPC>();
        private StoredData storedData;
        
        [PluginReference] private Plugin Economics;
        [PluginReference] private Plugin ServerRewards;
        [PluginReference] private Plugin NpcSpawn;
        #endregion
        
        #region Configuration
        private class Configuration
        {
            [JsonProperty("NPC Hiring Cost")]
            public int HiringCost = 500;
            
            [JsonProperty("Use Economics for payments")]
            public bool UseEconomics = false;
            
            [JsonProperty("Use ServerRewards for payments")]
            public bool UseServerRewards = true;
            
            [JsonProperty("Max NPCs per player")]
            public int MaxNPCsPerPlayer = 3;
            
            [JsonProperty("NPC Types")]
            public Dictionary<string, NPCTypeConfig> NPCTypes = new Dictionary<string, NPCTypeConfig>
            {
                ["Defender"] = new NPCTypeConfig 
                { 
                    Name = "Defender", 
                    BasePrefab = "assets/prefabs/npc/scientist/scientist.prefab",
                    BaseHealth = 150,
                    BaseAttackDamage = 25,
                    DefaultWeapon = "rifle.ak",
                    GatherMultiplier = 0.5f,
                    SkillTree = new List<string> { "Ranged", "Melee", "Health" }
                },
                ["Gatherer"] = new NPCTypeConfig 
                { 
                    Name = "Gatherer", 
                    BasePrefab = "assets/prefabs/npc/scientist/scientist.prefab",
                    BaseHealth = 100,
                    BaseAttackDamage = 15,
                    DefaultWeapon = "pickaxe",
                    GatherMultiplier = 2.0f,
                    SkillTree = new List<string> { "Gathering", "Speed", "Capacity" }
                },
                ["Medic"] = new NPCTypeConfig 
                { 
                    Name = "Medic", 
                    BasePrefab = "assets/prefabs/npc/scientist/scientist.prefab",
                    BaseHealth = 120,
                    BaseAttackDamage = 10,
                    DefaultWeapon = "pistol.semiauto",
                    GatherMultiplier = 0.7f,
                    SkillTree = new List<string> { "Healing", "Health", "Speed" }
                }
            };
            
            [JsonProperty("Skills")]
            public Dictionary<string, SkillConfig> Skills = new Dictionary<string, SkillConfig>
            {
                ["Ranged"] = new SkillConfig { MaxLevel = 5, CostPerLevel = 100, EffectPerLevel = 10 },
                ["Melee"] = new SkillConfig { MaxLevel = 5, CostPerLevel = 100, EffectPerLevel = 15 },
                ["Health"] = new SkillConfig { MaxLevel = 5, CostPerLevel = 100, EffectPerLevel = 25 },
                ["Gathering"] = new SkillConfig { MaxLevel = 5, CostPerLevel = 100, EffectPerLevel = 0.2f },
                ["Speed"] = new SkillConfig { MaxLevel = 5, CostPerLevel = 100, EffectPerLevel = 0.1f },
                ["Capacity"] = new SkillConfig { MaxLevel = 5, CostPerLevel = 100, EffectPerLevel = 2 },
                ["Healing"] = new SkillConfig { MaxLevel = 5, CostPerLevel = 100, EffectPerLevel = 5 }
            };
            
            [JsonProperty("Experience Settings")]
            public ExperienceConfig Experience = new ExperienceConfig
            {
                BaseExpPerGather = 5,
                BaseExpPerKill = 20,
                ExpPerLevel = 100,
                MaxLevel = 20
            };
            
            [JsonProperty("Gathering Settings")]
            public GatheringConfig Gathering = new GatheringConfig
            {
                GatheringRadius = 5f,
                GatheringInterval = 5f,
                ResourceMultiplier = 1.0f,
                AllowedResources = new List<string> 
                { 
                    "stone-ore", "sulfur-ore", "metal-ore", "wood", "hemp", "pumpkin", "corn" 
                }
            };
            
            [JsonProperty("Combat Settings")]
            public CombatConfig Combat = new CombatConfig
            {
                FollowDistance = 5f,
                AttackRange = 50f,
                AttackInterval = 2f,
                TargetPlayers = false,
                TargetAnimals = true,
                TargetNPCs = true
            };
        }
        
        public class NPCTypeConfig
        {
            public string Name;
            public string BasePrefab;
            public int BaseHealth;
            public float BaseAttackDamage;
            public string DefaultWeapon;
            public float GatherMultiplier;
            public List<string> SkillTree;
        }
        
        public class SkillConfig
        {
            public int MaxLevel;
            public int CostPerLevel;
            public float EffectPerLevel;
        }
        
        public class ExperienceConfig
        {
            public int BaseExpPerGather;
            public int BaseExpPerKill;
            public int ExpPerLevel;
            public int MaxLevel;
        }
        
        public class GatheringConfig
        {
            public float GatheringRadius;
            public float GatheringInterval;
            public float ResourceMultiplier;
            public List<string> AllowedResources;
        }
        
        public class CombatConfig
        {
            public float FollowDistance;
            public float AttackRange;
            public float AttackInterval;
            public bool TargetPlayers;
            public bool TargetAnimals;
            public bool TargetNPCs;
        }
        
        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
            }
            catch
            {
                PrintError("Configuration file is corrupt! Loading default configuration...");
                LoadDefaultConfig();
            }
            SaveConfig();
        }
        
        protected override void LoadDefaultConfig()
        {
            config = new Configuration();
        }
        
        protected override void SaveConfig()
        {
            Config.WriteObject(config);
        }
        #endregion
        
        #region Data Management
        private class StoredData
        {
            public Dictionary<ulong, List<SavedNPC>> SavedNPCs = new Dictionary<ulong, List<SavedNPC>>();
        }
        
        private class SavedNPC
        {
            public string Type;
            public int Level;
            public int Experience;
            public Dictionary<string, int> Skills = new Dictionary<string, int>();
            public List<SavedItem> Inventory = new List<SavedItem>();
        }
        
        private class SavedItem
        {
            public string ShortName;
            public int Amount;
            public ulong SkinId;
        }
        
        private void LoadData()
        {
            try
            {
                storedData = Interface.Oxide.DataFileSystem.ReadObject<StoredData>("HireableNPCs");
            }
            catch
            {
                storedData = new StoredData();
            }
        }
        
        private void SaveData()
        {
            Interface.Oxide.DataFileSystem.WriteObject("HireableNPCs", storedData);
        }
        #endregion
        
        #region NPC Classes
        private class HireableNPC
        {
            public NetworkableId EntityId;
            public ulong OwnerId;
            public string Type;
            public int Level;
            public int Experience;
            public Dictionary<string, int> Skills = new Dictionary<string, int>();
            public Timer GatheringTimer;
            public Timer CombatTimer;
            public Timer FollowTimer;
            public BasePlayer OwnerPlayer;
            public NPCPlayer NpcEntity;
            public Vector3 HomePosition;
            public bool IsFollowing;
            public bool IsGathering;
            public bool IsPatrolling;
            public float LastAttackTime;
            public List<Item> Inventory = new List<Item>();
            
            // Reference to the plugin instance for timer access
            private HireableNPCs plugin;
            
            public HireableNPC(HireableNPCs pluginInstance)
            {
                plugin = pluginInstance;
            }
            
            public void StartFollowing()
            {
                IsFollowing = true;
                IsGathering = false;
                IsPatrolling = false;
                
                if (FollowTimer != null)
                    FollowTimer.Destroy();
                    
                FollowTimer = plugin.timer.Every(1f, () => {
                    if (OwnerPlayer == null || !OwnerPlayer.IsConnected || NpcEntity == null || NpcEntity.IsDead())
                    {
                        FollowTimer?.Destroy();
                        return;
                    }
                    
                    float distance = Vector3.Distance(NpcEntity.transform.position, OwnerPlayer.transform.position);
                    if (distance > 5f)
                    {
                        Vector3 targetPos = OwnerPlayer.transform.position;
                        NpcEntity.GetComponent<BaseNavigator>()?.SetDestination(targetPos, BaseNavigator.NavigationSpeed.Normal);
                    }
                });
            }
            
            public void StartPatrolling()
            {
                IsFollowing = false;
                IsGathering = false;
                IsPatrolling = true;
                
                if (FollowTimer != null)
                    FollowTimer.Destroy();
                    
                FollowTimer = plugin.timer.Every(5f, () => {
                    if (NpcEntity == null || NpcEntity.IsDead())
                    {
                        FollowTimer?.Destroy();
                        return;
                    }
                    
                    // Get random position within patrol radius
                    Vector3 randomPos = GetRandomPositionAround(HomePosition, 15f);
                    NpcEntity.GetComponent<BaseNavigator>()?.SetDestination(randomPos, BaseNavigator.NavigationSpeed.Slow);
                });
            }
            
            public void StartGathering()
            {
                IsFollowing = false;
                IsGathering = true;
                IsPatrolling = false;
                
                if (GatheringTimer != null)
                    GatheringTimer.Destroy();
                    
                GatheringTimer = plugin.timer.Every(5f, () => {
                    if (NpcEntity == null || NpcEntity.IsDead())
                    {
                        GatheringTimer?.Destroy();
                        return;
                    }
                    
                    // Find nearby resources
                    List<BaseEntity> resources = new List<BaseEntity>();
                    Vis.Entities(NpcEntity.transform.position, 10f, resources, LayerMask.GetMask("Default", "Resource"));
                    
                    BaseEntity targetResource = null;
                    foreach (var entity in resources)
                    {
                        if (entity is ResourceEntity || entity is TreeEntity)
                        {
                            targetResource = entity;
                            break;
                        }
                    }
                    
                    if (targetResource != null)
                    {
                        // Move to resource
                        NpcEntity.GetComponent<BaseNavigator>()?.SetDestination(targetResource.transform.position, BaseNavigator.NavigationSpeed.Normal);
                        
                        // If close enough, gather
                        if (Vector3.Distance(NpcEntity.transform.position, targetResource.transform.position) < 2f)
                        {
                            GatherResource(targetResource);
                        }
                    }
                    else
                    {
                        // No resources found, move to random position
                        Vector3 randomPos = GetRandomPositionAround(NpcEntity.transform.position, 10f);
                        NpcEntity.GetComponent<BaseNavigator>()?.SetDestination(randomPos, BaseNavigator.NavigationSpeed.Normal);
                    }
                });
            }
            
            public void StartCombat()
            {
                if (CombatTimer != null)
                    CombatTimer.Destroy();
                    
                CombatTimer = plugin.timer.Every(1f, () => {
                    if (NpcEntity == null || NpcEntity.IsDead())
                    {
                        CombatTimer?.Destroy();
                        return;
                    }
                    
                    // Find potential targets
                    List<BaseEntity> entities = new List<BaseEntity>();
                    Vis.Entities(NpcEntity.transform.position, 30f, entities);
                    
                    BaseEntity target = null;
                    float closestDistance = float.MaxValue;
                    
                    foreach (var entity in entities)
                    {
                        if (CanTarget(entity))
                        {
                            float distance = Vector3.Distance(NpcEntity.transform.position, entity.transform.position);
                            if (distance < closestDistance)
                            {
                                closestDistance = distance;
                                target = entity;
                            }
                        }
                    }
                    
                    if (target != null && Time.time - LastAttackTime > 2f)
                    {
                        // Move to target if too far
                        if (closestDistance > 5f)
                        {
                            NpcEntity.GetComponent<BaseNavigator>()?.SetDestination(target.transform.position, BaseNavigator.NavigationSpeed.Fast);
                        }
                        
                        // Attack if in range
                        if (closestDistance < 20f)
                        {
                            AttackTarget(target);
                            LastAttackTime = Time.time;
                        }
                    }
                });
            }
            
            private bool CanTarget(BaseEntity entity)
            {
                if (entity == null || entity.IsDestroyed)
                    return false;
                    
                if (entity is BasePlayer player)
                {
                    // Don't target owner or teammates
                    if (player.userID == OwnerId || player.IsNpc)
                        return false;
                        
                    return true;
                }
                
                if (entity is BaseAnimalNPC)
                    return true;
                    
                return false;
            }
            
            private void AttackTarget(BaseEntity target)
            {
                if (NpcEntity == null || target == null)
                    return;
                    
                // Face target
                Vector3 direction = (target.transform.position - NpcEntity.transform.position).normalized;
                NpcEntity.eyes.rotation = Quaternion.LookRotation(direction);
                
                // Get weapon
                Item weapon = NpcEntity.GetActiveItem();
                if (weapon == null)
                    return;
                    
                // Attack based on weapon type
                BaseProjectile projectile = weapon.GetHeldEntity() as BaseProjectile;
                if (projectile != null)
                {
                    // Ranged attack
                    projectile.ServerUse();
                }
                else
                {
                    // Melee attack
                    BaseMelee melee = weapon.GetHeldEntity() as BaseMelee;
                    if (melee != null)
                    {
                        melee.ServerUse();
                        
                        // Apply damage directly for melee
                        float damage = 25f * (1f + GetSkillLevel("Melee") * 0.2f);
                        if (target is BaseCombatEntity combatEntity)
                        {
                            combatEntity.Hurt(damage, Rust.DamageType.Slash, NpcEntity, false);
                        }
                    }
                }
            }
            
            private void GatherResource(BaseEntity resource)
            {
                if (NpcEntity == null || resource == null)
                    return;
                    
                // Get gathering skill bonus
                float gatherMultiplier = 1f + GetSkillLevel("Gathering") * 0.2f;
                
                // Different handling based on resource type
                if (resource is TreeEntity)
                {
                    // Gather wood
                    int amount = Mathf.RoundToInt(10f * gatherMultiplier);
                    AddItemToInventory("wood", amount);
                    
                    // Add experience
                    AddExperience(5);
                }
                else if (resource is ResourceEntity)
                {
                    // Determine resource type
                    string resourceName = "stone";
                    if (resource.ShortPrefabName.Contains("metal"))
                        resourceName = "metal.ore";
                    else if (resource.ShortPrefabName.Contains("sulfur"))
                        resourceName = "sulfur.ore";
                        
                    // Gather resource
                    int amount = Mathf.RoundToInt(5f * gatherMultiplier);
                    AddItemToInventory(resourceName, amount);
                    
                    // Add experience
                    AddExperience(5);
                }
            }
            
            public void AddItemToInventory(string itemName, int amount)
            {
                if (NpcEntity == null)
                    return;
                    
                // Create item
                Item item = ItemManager.CreateByName(itemName, amount);
                if (item == null)
                    return;
                    
                // Add to NPC inventory
                Inventory.Add(item);
                
                // Transfer to owner if nearby
                if (OwnerPlayer != null && OwnerPlayer.IsConnected && 
                    Vector3.Distance(NpcEntity.transform.position, OwnerPlayer.transform.position) < 5f)
                {
                    OwnerPlayer.inventory.GiveItem(item);
                }
            }
            
            public void AddExperience(int amount)
            {
                Experience += amount;
                
                // Check for level up
                int expForNextLevel = 100 + (Level * 50);
                if (Experience >= expForNextLevel && Level < 20)
                {
                    Level++;
                    Experience -= expForNextLevel;
                }
            }
            
            public int GetSkillLevel(string skill)
            {
                if (Skills.ContainsKey(skill))
                    return Skills[skill];
                    
                return 0;
            }
            
            public bool UpgradeSkill(string skill)
            {
                if (!Skills.ContainsKey(skill))
                    Skills[skill] = 0;
                    
                if (Skills[skill] >= 5)
                    return false;
                    
                Skills[skill]++;
                return true;
            }
            
            private Vector3 GetRandomPositionAround(Vector3 center, float radius)
            {
                Vector2 randomCircle = UnityEngine.Random.insideUnitCircle * radius;
                return new Vector3(center.x + randomCircle.x, center.y, center.z + randomCircle.y);
            }
        }
        #endregion
        
        #region Hooks
        private void Init()
        {
        if (NpcSpawn == null)
        {
        PrintError("NpcSpawn plugin is required but not found! Disabling HireableNPCs.");
        Interface.Oxide.UnloadPlugin(Name);
        return;
    }
}
        private void OnServerInitialized()
        {
            // Register permissions
            permission.RegisterPermission("hireablenpcs.use", this);
            permission.RegisterPermission("hireablenpcs.admin", this);
            
            // Register commands
            cmd.AddChatCommand("npc", this, "CmdNPC");
            cmd.AddChatCommand("npcs", this, "CmdNPCs");
            
            // Load data
            LoadData();
            
            // Restore saved NPCs
            RestoreSavedNPCs();
            
            Puts("Hireable NPCs plugin initialized!");
        }
        
        void Unload()
        {
            // Save all NPCs
            SaveAllNPCs();
            
            // Destroy all active NPCs
            foreach (var npc in activeNPCs.Values)
            {
                DestroyNPC(npc);
            }
            
            // Clear collections
            playerNPCs.Clear();
            activeNPCs.Clear();
        }
        
        void OnEntityDeath(BaseCombatEntity entity, HitInfo info)
        {
            // Handle NPC death
            if (entity is NPCPlayer npcPlayer && activeNPCs.ContainsKey(entity.net.ID))
            {
                var npc = activeNPCs[entity.net.ID];
                HandleNPCDeath(npc, info);
            }
            
            // Handle NPC kills
            if (info?.InitiatorPlayer is NPCPlayer attacker && activeNPCs.ContainsKey(attacker.net.ID))
            {
                var npc = activeNPCs[attacker.net.ID];
                HandleNPCKill(npc, entity);
            }
        }
        
        void OnEntityGather(ResourceDispenser dispenser, BaseEntity entity, Item item)
        {
            // Handle NPC gathering
            if (entity is NPCPlayer npcPlayer && activeNPCs.ContainsKey(entity.net.ID))
            {
                var npc = activeNPCs[entity.net.ID];
                HandleNPCGather(npc, item);
            }
        }
        #endregion
        
        #region Commands
        private void CmdNPC(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, "hireablenpcs.use"))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            if (args.Length == 0)
            {
                SendReply(player, "Available commands: /npc hire [type], /npc dismiss [id], /npc list, /npc skills [id], /npc upgrade [id] [skill]");
                return;
            }
            
            switch (args[0].ToLower())
            {
                case "hire":
                    HireNPC(player, args.Length > 1 ? args[1] : "");
                    break;
                case "dismiss":
                    DismissNPC(player, args.Length > 1 ? args[1] : "");
                    break;
                case "list":
                    ListNPCs(player);
                    break;
                case "skills":
                    ShowNPCSkills(player, args.Length > 1 ? args[1] : "");
                    break;
                case "upgrade":
                    UpgradeNPCSkill(player, args.Length > 1 ? args[1] : "", args.Length > 2 ? args[2] : "");
                    break;
                default:
                    SendReply(player, "Unknown command. Available commands: /npc hire [type], /npc dismiss [id], /npc list, /npc skills [id], /npc upgrade [id] [skill]");
                    break;
            }
        }
        
        private void CmdNPCs(BasePlayer player, string command, string[] args)
        {
            // Shortcut for /npc list
            ListNPCs(player);
        }
        #endregion
        
        #region NPC Management Methods
        private void HireNPC(BasePlayer player, string type)
        {
            // Check if player has reached max NPCs
            if (!playerNPCs.ContainsKey(player.userID))
                playerNPCs[player.userID] = new List<HireableNPC>();
                
            if (playerNPCs[player.userID].Count >= config.MaxNPCsPerPlayer)
            {
                SendReply(player, $"You've reached the maximum number of NPCs ({config.MaxNPCsPerPlayer}).");
                return;
            }
            
            // Validate NPC type
            if (string.IsNullOrEmpty(type) || !config.NPCTypes.ContainsKey(type))
            {
                string availableTypes = string.Join(", ", config.NPCTypes.Keys);
                SendReply(player, $"Invalid NPC type. Available types: {availableTypes}");
                return;
            }
            
            // Check payment
            if (!TakePayment(player, config.HiringCost))
            {
                SendReply(player, $"You need {config.HiringCost} coins to hire an NPC.");
                return;
            }
            
            // Spawn NPC
            SpawnNPC(player, type);
            
            SendReply(player, $"You hired a {type} NPC. Use /npc list to see your NPCs.");
        }

        private void SpawnNPC(BasePlayer owner, string type)
        {
            NPCTypeConfig typeConfig = config.NPCTypes[type];

            Vector3 spawnPos = owner.transform.position + (owner.transform.forward * 2f);

            object npcObj = NpcSpawn?.Call("SpawnNPC", typeConfig.BasePrefab, spawnPos, Quaternion.identity);
            if (npcObj == null)
            {
                SendReply(owner, "Failed to spawn NPC using NpcSpawn.");
                return;
            }

            BaseEntity entity = npcObj as BaseEntity;
            if (entity == null)
            {
                SendReply(owner, "Spawned NPC is not a valid entity.");
                return;
            }

            NPCPlayer npcEntity = entity as NPCPlayer;
            if (npcEntity == null)
            {
                npcEntity = entity.GetComponent<NPCPlayer>();
                if (npcEntity == null)
            {
                SendReply(owner, "Spawned entity is not an NPCPlayer.");
                entity.Kill();
                return;
            }
        }

        npcEntity.displayName = $"{owner.displayName}'s {typeConfig.Name}";
        npcEntity.SetMaxHealth(typeConfig.BaseHealth);
        npcEntity.health = typeConfig.BaseHealth;

        // Give weapon
        if (!string.IsNullOrEmpty(typeConfig.DefaultWeapon))
        {
            Item weapon = ItemManager.CreateByName(typeConfig.DefaultWeapon);
            if (weapon != null)
            {
                npcEntity.inventory.containerBelt.Insert(weapon);
                npcEntity.UpdateActiveItem(weapon.uid);
            }
        }

        HireableNPC npc = new HireableNPC(this)
    {
            EntityId = npcEntity.net.ID,
            OwnerId = owner.userID,
            Type = type,
            Level = 1,
            Experience = 0,
            OwnerPlayer = owner,
            NpcEntity = npcEntity,
            HomePosition = owner.transform.position,
            LastAttackTime = 0f
    };

    foreach (string skill in typeConfig.SkillTree)
    {
        npc.Skills[skill] = 1;
    }

    npc.StartFollowing();
    npc.StartCombat();

    if (!playerNPCs.ContainsKey(owner.userID))
        playerNPCs[owner.userID] = new List<HireableNPC>();

    activeNPCs[npcEntity.net.ID] = npc;
    playerNPCs[owner.userID].Add(npc);

    SaveData();
}

            NPCTypeConfig typeConfig = config.NPCTypes[type];
            
            // Create NPC entity
            NPCPlayer npcEntity = GameManager.server.CreateEntity(typeConfig.BasePrefab, owner.transform.position + (owner.transform.forward * 2f)) as NPCPlayer;
            if (npcEntity == null)
                return;
                
            // Set NPC properties
            npcEntity.displayName = $"{owner.displayName}'s {typeConfig.Name}";
            npcEntity.SetMaxHealth(typeConfig.BaseHealth);
            npcEntity.health = typeConfig.BaseHealth;
            
            // Spawn NPC
            npcEntity.Spawn();
            
            // Give weapon
            if (!string.IsNullOrEmpty(typeConfig.DefaultWeapon))
            {
                Item weapon = ItemManager.CreateByName(typeConfig.DefaultWeapon);
                if (weapon != null)
                {
                    npcEntity.inventory.containerBelt.Insert(weapon);
                    npcEntity.UpdateActiveItem(weapon.uid);
                }
            }
            
            // Create HireableNPC instance
            HireableNPC npc = new HireableNPC(this)
            {
                EntityId = npcEntity.net.ID,
                OwnerId = owner.userID,
                Type = type,
                Level = 1,
                Experience = 0,
                OwnerPlayer = owner,
                NpcEntity = npcEntity,
                HomePosition = owner.transform.position,
                LastAttackTime = 0f
            };
            
            // Initialize skills
            foreach (string skill in typeConfig.SkillTree)
            {
                npc.Skills[skill] = 1;
            }
            
            // Start behaviors
            npc.StartFollowing();
            npc.StartCombat();
            
            // Add to collections
            activeNPCs[npcEntity.net.ID] = npc;
            playerNPCs[owner.userID].Add(npc);
            
            // Save data
            SaveData();
        }

        private void DismissNPC(BasePlayer player, string idStr)
        {
            if (!playerNPCs.ContainsKey(player.userID) || playerNPCs[player.userID].Count == 0)
            {
                SendReply(player, "You don't have any NPCs.");
                return;
            }
            
            // Try parse ID
            int id;
            if (!int.TryParse(idStr, out id) || id < 1 || id > playerNPCs[player.userID].Count)
            {
                SendReply(player, "Invalid NPC ID. Use /npc list to see your NPCs.");
                return;
            }
            
            // Get NPC
            HireableNPC npc = playerNPCs[player.userID][id - 1];
            
            // Destroy NPC
            DestroyNPC(npc);
            
            // Remove from collections
            playerNPCs[player.userID].Remove(npc);
            activeNPCs.Remove(npc.EntityId);
            
            SendReply(player, $"You dismissed your {npc.Type} NPC.");
            
            // Save data
            SaveData();
        }

        private void ListNPCs(BasePlayer player)
        {
            if (!playerNPCs.ContainsKey(player.userID) || playerNPCs[player.userID].Count == 0)
            {
                SendReply(player, "You don't have any NPCs.");
                return;
            }
            
            SendReply(player, "Your NPCs:");
            
            for (int i = 0; i < playerNPCs[player.userID].Count; i++)
            {
                HireableNPC npc = playerNPCs[player.userID][i];
                string status = npc.IsFollowing ? "Following" : npc.IsGathering ? "Gathering" : "Patrolling";
                SendReply(player, $"{i + 1}. {npc.Type} (Level {npc.Level}) - {status}");
            }
        }

        private void ShowNPCSkills(BasePlayer player, string idStr)
        {
            if (!playerNPCs.ContainsKey(player.userID) || playerNPCs[player.userID].Count == 0)
            {
                SendReply(player, "You don't have any NPCs.");
                return;
            }
            
            // Try parse ID
            int id;
            if (!int.TryParse(idStr, out id) || id < 1 || id > playerNPCs[player.userID].Count)
            {
                SendReply(player, "Invalid NPC ID. Use /npc list to see your NPCs.");
                return;
            }
            
            // Get NPC
            HireableNPC npc = playerNPCs[player.userID][id - 1];
            
            SendReply(player, $"{npc.Type} NPC (Level {npc.Level}) - XP: {npc.Experience}/{100 + (npc.Level * 50)}");
            SendReply(player, "Skills:");
            
            foreach (var skill in npc.Skills)
            {
                SendReply(player, $"- {skill.Key}: Level {skill.Value}/5");
            }
        }

        private void UpgradeNPCSkill(BasePlayer player, string idStr, string skill)
        {
            if (!playerNPCs.ContainsKey(player.userID) || playerNPCs[player.userID].Count == 0)
            {
                SendReply(player, "You don't have any NPCs.");
                return;
            }
            
            // Try parse ID
            int id;
            if (!int.TryParse(idStr, out id) || id < 1 || id > playerNPCs[player.userID].Count)
            {
                SendReply(player, "Invalid NPC ID. Use /npc list to see your NPCs.");
                return;
            }
            
            // Get NPC
            HireableNPC npc = playerNPCs[player.userID][id - 1];
            
            // Check if skill exists
            if (!npc.Skills.ContainsKey(skill))
            {
                string availableSkills = string.Join(", ", npc.Skills.Keys);
                SendReply(player, $"Invalid skill. Available skills: {availableSkills}");
                return;
            }
            
            // Check if skill is maxed
            if (npc.Skills[skill] >= 5)
            {
                SendReply(player, $"The {skill} skill is already at maximum level (5).");
                return;
            }
            
            // Check payment
            int cost = config.Skills[skill].CostPerLevel * npc.Skills[skill];
            if (!TakePayment(player, cost))
            {
                SendReply(player, $"You need {cost} coins to upgrade this skill.");
                return;
            }
            
            // Upgrade skill
            npc.Skills[skill]++;
            
            SendReply(player, $"Upgraded {skill} skill to level {npc.Skills[skill]}/5.");
            
            // Save data
            SaveData();
        }

        private void DestroyNPC(HireableNPC npc)
        {
            // Stop timers
            npc.FollowTimer?.Destroy();
            npc.GatheringTimer?.Destroy();
            npc.CombatTimer?.Destroy();
            
            // Kill NPC entity
            if (npc.NpcEntity != null && !npc.NpcEntity.IsDestroyed)
            {
                npc.NpcEntity.Kill();
            }
        }

        private void HandleNPCDeath(HireableNPC npc, HitInfo info)
        {
            // Notify owner
            if (npc.OwnerPlayer != null && npc.OwnerPlayer.IsConnected)
            {
                SendReply(npc.OwnerPlayer, $"Your {npc.Type} NPC has died!");
            }
            
            // Remove from collections
            if (playerNPCs.ContainsKey(npc.OwnerId))
            {
                playerNPCs[npc.OwnerId].Remove(npc);
            }
            
            activeNPCs.Remove(npc.EntityId);
            
            // Stop timers
            npc.FollowTimer?.Destroy();
            npc.GatheringTimer?.Destroy();
            npc.CombatTimer?.Destroy();
            
            // Save data
            SaveData();
        }

        private void HandleNPCKill(HireableNPC npc, BaseCombatEntity victim)
        {
            // Add experience for kill
            npc.AddExperience(config.Experience.BaseExpPerKill);
            
            // Notify owner if nearby
            if (npc.OwnerPlayer != null && npc.OwnerPlayer.IsConnected && 
                Vector3.Distance(npc.NpcEntity.transform.position, npc.OwnerPlayer.transform.position) < 30f)
            {
                SendReply(npc.OwnerPlayer, $"Your {npc.Type} NPC killed {victim.ShortPrefabName} (+{config.Experience.BaseExpPerKill} XP)");
            }
            
            // Save data
            SaveData();
        }

        private void HandleNPCGather(HireableNPC npc, Item item)
        {
            // Add experience for gathering
            npc.AddExperience(config.Experience.BaseExpPerGather);
            
            // Apply gathering multiplier
            NPCTypeConfig typeConfig = config.NPCTypes[npc.Type];
            item.amount = Mathf.RoundToInt(item.amount * typeConfig.GatherMultiplier * (1f + npc.GetSkillLevel("Gathering") * 0.2f));
            
            // Notify owner if nearby
            if (npc.OwnerPlayer != null && npc.OwnerPlayer.IsConnected && 
                Vector3.Distance(npc.NpcEntity.transform.position, npc.OwnerPlayer.transform.position) < 30f)
            {
                SendReply(npc.OwnerPlayer, $"Your {npc.Type} NPC gathered {item.info.shortname} (+{config.Experience.BaseExpPerGather} XP)");
            }
            
            // Save data
            SaveData();
        }
        #endregion

        private void RestoreSavedNPCs()
        {
            // Implementation based on your plugin's needs
            if (storedData?.SavedNPCs == null) return;
            
            foreach (var entry in storedData.SavedNPCs)
            {
                ulong playerId = entry.Key;
                
                foreach (var savedNpc in entry.Value)
                {
                    // Restore NPCs from saved data
                    // Implementation depends on your data structure
                }
            }
        }

        private void SaveAllNPCs()
        {
            // Clear existing saved data
            storedData = new StoredData();
            
            // Save all active NPCs to the stored data
            foreach (var entry in playerNPCs)
            {
                ulong playerId = entry.Key;
                List<SavedNPC> savedNPCs = new List<SavedNPC>();
                
                foreach (var npc in entry.Value)
                {
                    // Skip invalid NPCs
                    if (npc == null || npc.NpcEntity == null)
                        continue;
                        
                    // Create saved NPC data
                    SavedNPC savedNPC = new SavedNPC
                    {
                        Type = npc.Type,
                        Level = npc.Level,
                        Experience = npc.Experience,
                        Skills = new Dictionary<string, int>(npc.Skills)
                    };
                    
                    // Save inventory items if needed
                    foreach (var item in npc.Inventory)
                    {
                        if (item != null)
                        {
                            savedNPC.Inventory.Add(new SavedItem
                            {
                                ShortName = item.info.shortname,
                                Amount = item.amount,
                                SkinId = item.skin
                            });
                        }
                    }
                    
                    savedNPCs.Add(savedNPC);
                }
                
                if (savedNPCs.Count > 0)
                {
                    storedData.SavedNPCs[playerId] = savedNPCs;
                }
            }
            
            // Save to disk
            SaveData();
        }
        
        private bool TakePayment(BasePlayer player, int amount)
        {
            // Check if player has permission to bypass payment
            if (permission.UserHasPermission(player.UserIDString, "hireablenpcs.admin"))
                return true;
                
            // Try to use Economics plugin if available and configured
            if (Economics != null && config.UseEconomics)
            {
                double balance = (double)Economics.Call("Balance", player.userID);
                if (balance >= amount)
                {
                    Economics.Call("Withdraw", player.userID, (double)amount);
                    return true;
                }
                return false;
            }
            
            // Try to use ServerRewards plugin if available and configured
            if (ServerRewards != null && config.UseServerRewards)
            {
                int points = (int)ServerRewards.Call("CheckPoints", player.userID);
                if (points >= amount)
                {
                    ServerRewards.Call("TakePoints", player.userID, amount);
                    return true;
                }
                return false;
            }
            
            // If no economy plugin is available or configured, allow free hiring
            return true;
        }
    }
}

public static class Layers
{
    public static class Mask
    {
        public static int Default => LayerMask.GetMask("Default");
        public static int Resource => LayerMask.GetMask("Resource");
        public static int Construction => LayerMask.GetMask("Construction");
        public static int Deployed => LayerMask.GetMask("Deployed");
        public static int Player => LayerMask.GetMask("Player (Server)");
    }
}

