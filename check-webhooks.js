const fs = require('fs');
const path = require('path');

const serversDir = path.resolve(__dirname, 'UltimateRcon/CONFIGS/SERVERS');
console.log(`Checking webhook configurations in: ${serversDir}`);

if (fs.existsSync(serversDir)) {
  const serverFiles = fs.readdirSync(serversDir).filter(file => file.endsWith('.json'));
  
  serverFiles.forEach(file => {
    try {
      const serverFilePath = path.join(serversDir, file);
      const serverConfig = JSON.parse(fs.readFileSync(serverFilePath, 'utf8'));
      
      console.log(`\nChecking webhooks for: ${serverConfig.SERVER_SHORTNAME}`);
      
      // Check chat logs webhooks
      if (serverConfig.CHAT_LOGS) {
        console.log("CHAT_LOGS:");
        if (serverConfig.CHAT_LOGS.GLOBAL_CHAT_LOGS) {
          console.log(`  GLOBAL_CHAT_LOGS.ENABLED: ${serverConfig.CHAT_LOGS.GLOBAL_CHAT_LOGS.ENABLED}`);
          console.log(`  GLOBAL_CHAT_WEBHOOK: ${serverConfig.CHAT_LOGS.GLOBAL_CHAT_LOGS.GLOBAL_CHAT_WEBHOOK ? 'Set' : 'Not set'}`);
        }
        if (serverConfig.CHAT_LOGS.TEAM_CHAT_LOGS) {
          console.log(`  TEAM_CHAT_LOGS.ENABLED: ${serverConfig.CHAT_LOGS.TEAM_CHAT_LOGS.ENABLED}`);
          console.log(`  TEAM_CHAT_WEBHOOK: ${serverConfig.CHAT_LOGS.TEAM_CHAT_LOGS.TEAM_CHAT_WEBHOOK ? 'Set' : 'Not set'}`);
        }
      }
      
      // Check server logging webhooks
      if (serverConfig.SERVER_LOGGING) {
        console.log("SERVER_LOGGING:");
        if (serverConfig.SERVER_LOGGING.SERVER_JOIN_LOGS) {
          console.log(`  SERVER_JOIN_LOGS.ENABLED: ${serverConfig.SERVER_LOGGING.SERVER_JOIN_LOGS.ENABLED}`);
          console.log(`  LOG_WEBHOOK: ${serverConfig.SERVER_LOGGING.SERVER_JOIN_LOGS.LOG_WEBHOOK ? 'Set' : 'Not set'}`);
        }
        if (serverConfig.SERVER_LOGGING.SERVER_LEAVE_LOGS) {
          console.log(`  SERVER_LEAVE_LOGS.ENABLED: ${serverConfig.SERVER_LOGGING.SERVER_LEAVE_LOGS.ENABLED}`);
          console.log(`  LOG_WEBHOOK: ${serverConfig.SERVER_LOGGING.SERVER_LEAVE_LOGS.LOG_WEBHOOK ? 'Set' : 'Not set'}`);
        }
      }
      
      // Check server online/offline webhooks
      if (serverConfig.SERVER_ONLINE_OFFLINE) {
        console.log("SERVER_ONLINE_OFFLINE:");
        console.log(`  ENABLED: ${serverConfig.SERVER_ONLINE_OFFLINE.ENABLED}`);
        if (serverConfig.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS) {
          console.log(`  ONLINE_WEBHOOK: ${serverConfig.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.WEBHOOK ? 'Set' : 'Not set'}`);
        }
        if (serverConfig.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS) {
          console.log(`  OFFLINE_WEBHOOK: ${serverConfig.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.WEBHOOK ? 'Set' : 'Not set'}`);
        }
      }
      
    } catch (error) {
      console.error(`Error checking webhooks for ${file}:`, error.message);
    }
  });
} else {
  console.log(`Servers directory not found: ${serversDir}`);
}

function sendSimpleMessage(message, hook) {
    console.log(`Sending simple message: ${message.substring(0, 50)}...`);
    try {
        hook.send(message);
    } catch (error) {
        console.error(`Error sending simple message: ${error.message}`);
    }
}

function sendFancyMessage(description, color, title, hook, author = undefined, thumbnail = undefined) {
    console.log(`Sending fancy message: ${title} - ${description.substring(0, 50)}...`);
    try {
        let embed = new discord.EmbedBuilder();
        if (description) embed.setDescription(description);
        if (color) embed.setColor(color);
        if (title) embed.setTitle(title);
        if (author) embed.setAuthor(author);
        if (thumbnail) embed.setThumbnail(thumbnail);
        embed.setTimestamp();
        hook.send({ embeds: [embed] });
    } catch (error) {
        console.error(`Error sending fancy message: ${error.message}`);
    }
}

function handlePlayerJoining(playerInfo, server) {
    console.log(`Player joining: ${playerInfo.name} (${playerInfo.steam_id})`);
    if(playerInfo) {
        db.run("update player_info set connections = ? where server_id = ? and steam_id = ?;", [playerInfo.connections+1, server.SERVER_SPECIAL_ID, playerInfo.steam_id]);
    }

    if(server.SERVER_LOGGING.SERVER_JOIN_LOGS.ENABLED) {
        console.log(`Join logs enabled for ${server.SERVER_SHORTNAME}, webhook: ${server.SERVER_LOGGING.SERVER_JOIN_LOGS.LOG_WEBHOOK ? 'Set' : 'Not set'}`);
        try {
            const hook = new discord.WebhookClient({ url: server.SERVER_LOGGING.SERVER_JOIN_LOGS.LOG_WEBHOOK });
            if(server.SERVER_LOGGING.SERVER_JOIN_LOGS.SIMPLE_FORMATTING) sendSimpleMessage(`✅ **[${playerInfo.name}](<${playerInfo.profile_url}>)** has connected to the server!`, hook);
            else {
                let desc = `**User:** [${playerInfo.name}](${playerInfo.profile_url})\n**SteamID:** [${playerInfo.steam_id}](${playerInfo.profile_url}) / [BM](https://www.battlemetrics.com/rcon/players?filter%5Bsearch%5D=${playerInfo.steam_id})`;
                let author = { name: `${playerInfo.name} has joined!`, iconURL: playerInfo.picture, url: playerInfo.profile_url };
                sendFancyMessage(desc, server.SERVER_LOGGING.SERVER_JOIN_LOGS.EMBED_COLOR, "Player Joined", hook, author, playerInfo.picture);
            }
        } catch (error) {
            console.error(`Error in handlePlayerJoining: ${error.message}`);
        }
    } else {
        console.log(`Join logs disabled for ${server.SERVER_SHORTNAME}`);
    }
}

function handlePlayerLeaving(playerInfo, server, reason = undefined) {
    console.log(`Player leaving: ${playerInfo.name} (${playerInfo.steam_id}), reason: ${reason || 'None'}`);
    if(server.SERVER_LOGGING.SERVER_LEAVE_LOGS.ENABLED) {
        console.log(`Leave logs enabled for ${server.SERVER_SHORTNAME}, webhook: ${server.SERVER_LOGGING.SERVER_LEAVE_LOGS.LOG_WEBHOOK ? 'Set' : 'Not set'}`);
        try {
            const hook = new discord.WebhookClient({ url: server.SERVER_LOGGING.SERVER_LEAVE_LOGS.LOG_WEBHOOK });
            if(server.SERVER_LOGGING.SERVER_LEAVE_LOGS.SIMPLE_FORMATTING) sendSimpleMessage(`❌ **[${playerInfo.name}](<${playerInfo.profile_url}>)** has disconnected from the server${reason == undefined ? "" : `: ${reason}`}`, hook);
            else {
                let desc = `**User:** [${playerInfo.name}](${playerInfo.profile_url})\n**SteamID:** [${playerInfo.steam_id}](${playerInfo.profile_url}) / [BM](https://www.battlemetrics.com/rcon/players?filter%5Bsearch%5D=${playerInfo.steam_id})\n**Reason:** ${reason == undefined ? "No reason..." : ` ${reason}`}`;
                let author = { name: `${playerInfo.name} has disconnected`, iconURL: playerInfo.picture, url: playerInfo.profile_url };
                sendFancyMessage(desc, server.SERVER_LOGGING.SERVER_LEAVE_LOGS.EMBED_COLOR, "Player Disconnected", hook, author, playerInfo.picture);
            }
        } catch (error) {
            console.error(`Error in handlePlayerLeaving: ${error.message}`);
        }
    } else {
        console.log(`Leave logs disabled for ${server.SERVER_SHORTNAME}`);
    }
}

async function handleChatMessage(messageContent, messageChannel, server, rcon) {
    console.log(`Chat message: Channel ${messageChannel}, Content: ${typeof messageContent === 'string' ? messageContent.substring(0, 50) : 'Object'}`);
    
    // Add more debugging here
    if (messageChannel === 0 && server.CHAT_LOGS && server.CHAT_LOGS.GLOBAL_CHAT_LOGS && server.CHAT_LOGS.GLOBAL_CHAT_LOGS.ENABLED) {
        console.log(`Global chat logs enabled for ${server.SERVER_SHORTNAME}, webhook: ${server.CHAT_LOGS.GLOBAL_CHAT_LOGS.GLOBAL_CHAT_WEBHOOK ? 'Set' : 'Not set'}`);
        // Rest of the function...
    } else {
        console.log(`Global chat logs disabled for ${server.SERVER_SHORTNAME} or messageChannel is not 0 (${messageChannel})`);
    }
    
    // Rest of the function...
}

rcon.on('message', async(message) => {
    console.log(`RCON message received: Type: ${message.Type}, Identifier: ${message.Identifier}`);
    console.log(`Message content: ${typeof message.content === 'string' ? message.content.substring(0, 50) : 'Object'}`);
    
    // Rest of the function...
});
