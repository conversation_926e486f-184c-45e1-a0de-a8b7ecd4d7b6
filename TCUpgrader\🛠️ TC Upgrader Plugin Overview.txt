🛠️ TC Upgrader Plugin Overview

📋 Description

The TC Upgrader plugin enhances Rust gameplay by allowing players to upgrade or repair all building blocks within a Tool Cupboard’s range with just one click — no more tedious hammering!

✨ Features

🔨 Upgrade Buttons for all building tiers: Wood, Stone, Metal, Armored

🛠️ Repair Button to fix all structures in range (optional)

🎨 Configurable UI: Position, size, spacing, and colors

📦 Resource Cost Calculation with optional multipliers

🚫 Disable Resource Requirement (optional)

🔐 Permission-Based Access

🔊 Sound Effects for both upgrades and repairs

🖼️ ImageLibrary Support for custom button images

🧾 Customizable Messages

💬 Chat Commands

Command

Description

Permission

/tcupgrade

Opens upgrade UI manually

tcupgrader.use

/tcrepair

Repairs all blocks in TC range

tcupgrader.use

/tcupgrader.position <horizontal|vertical> <value>

Adjusts UI position

tcupgrader.admin

/tcupgrader.reload

Reloads plugin config

tcupgrader.admin

💻 Console Commands

Command

Description

Permission

tcupgrade <grade>

Upgrades blocks to specified grade (e.g., wood, stone)

tcupgrader.use

tcrepair

Repairs all blocks in TC range

tcupgrader.use

🔐 Permissions

Permission

Description

Default

tcupgrader.use

Use upgrade/repair features

None

tcupgrader.admin

Use admin and config commands

None

⚙️ Configuration Options

🔧 Resource Settings

Require Resources For Upgrades: true/false

Require Resources For Repairs: true/false

Enable Cost Multiplier: true/false

Cost Multiplier: 1.0 (default)

Repair Cost Multiplier: 0.5 (default)

Allow Downgrading: false (default)

🔑 Permission Settings

Enable Permission Requirement: true/false

🖼️ UI Settings

UI Position: top, bottom, left, right

UI Height, UI Width

UI Horizontal Offset, UI Vertical Offset

Button Size, Button Spacing

Show Title: true/false

Title Text, Title Font Size

🛠️ Repair Settings

Enable Repair Option: true/false

Show Repair Button: true/false

Repair Button Color: Hex or RGBA

🔊 Sound Settings

Enable Repair Sounds: true/false

Enable Upgrade Sounds: true/false

Sound Volume: 0.0 - 1.0

🖼️ Image Settings

Use Custom Images: true/false

Custom image URLs for each tier (wood, stone, etc.)

📣 Message Settings

Enable Custom Messages: true/false

Custom success/failure messages, notifications

🎮 Usage

👷 For Players:

Open a Tool Cupboard.

Click upgrade buttons (Wood, Stone, Metal, Armored) to upgrade all nearby blocks.

Click repair to fix damaged blocks.

Ensure you have required resources (unless disabled).

🛡️ For Admins:

Use /tcupgrader.reload to reload config.

Adjust UI layout with /tcupgrader.position.

Customize all settings via the config file.


🔧 UI Position Examples

Use these commands to fine-tune the TC Upgrader UI placement:

Command

Effect

tcupgrader.position horizontal 0.1

▶️ Move UI Right

tcupgrader.position horizontal -0.1

◀️ Move UI Left

tcupgrader.position vertical 0.1

🔼 Move UI Up

tcupgrader.position vertical -0.1

🔽 Move UI Down

🔍 Technical Details

Hooks into OnLootEntity to show UI on TC open.

Cleans up UI with OnPlayerLootEnd.

Calculates upgrade/repair costs dynamically.

Integrates with ImageLibrary for custom visuals.

Supports optional sound effects for immersive feedback.

✅ Ideal For

Quality-of-life improvements

Creative & PvE servers

Admin-controlled building upgrades

Custom UIs with themed visuals