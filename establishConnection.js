const rustrcon = require('rustrcon');
function fixWebhookUrl(url) {
  if (!url) return null;
  
  // Remove any whitespace
  url = url.trim();
  
  // Check if it's a valid Discord webhook URL
  if (!url.startsWith('https://discord.com/api/webhooks/')) {
    console.error(`Invalid webhook URL format: ${url}`);
    return null;
  }
  
  return url;
}
const path = require('path');
const configDir = path.resolve(__dirname, '../../CONFIGS');
const serversDir = path.join(configDir, 'SERVERS');
const configFile = path.join(configDir, 'config.json');
const config = require('../../CONFIGS/config.json');
const discord = require('discord.js');
const chalk = require('chalk');
const fs = require('fs');
const { loadImage, createCanvas } = require('canvas');
const fetch = require('node-fetch');
const sqlite3 = require('sqlite3');
const moment = require('moment');
const { REST } = require('@discordjs/rest');
const { readdirSync } = require("fs");
const { Routes } = require('discord-api-types/v10');

// Add a function to validate and fix webhook URLs
function validateWebhookUrl(url) {
  if (!url) return false;
  
  // Remove any whitespace
  url = url.trim();
  
  // Check if it's a valid Discord webhook URL
  if (!url.startsWith('https://discord.com/api/webhooks/')) {
    console.error(`Invalid webhook URL format: ${url}`);
    return false;
  }
  
  return true;
}

// Add a function to log webhook errors with server info
function logWebhookError(server, error) {
  console.error(`[${server.SERVER_SHORTNAME}] Webhook error: ${error.message}`);
  console.error(`[${server.SERVER_SHORTNAME}] Please use !fix${server.SERVER_SHORTNAME.replace(/\s+/g, '')}webhook to set a valid webhook URL`);
}

// Create webhook client with validation
function safeWebhookClient(url) {
  try {
    const validUrl = fixWebhookUrl(url);
    if (!validUrl) {
      console.error("Invalid webhook URL, cannot create client");
      return null;
    }
    
    return new discord.WebhookClient({ url: validUrl });
  } catch (err) {
    console.error("Error creating webhook client:", err.message);
    return null;
  }
}

// Safe function to send webhook messages
function sendWebhookMessage(server, webhookUrl, content, embeds = null) {
  try {
    if (!webhookUrl) {
      console.log(`[${server.SERVER_SHORTNAME}] No webhook URL provided`);
      return false;
    }
    
    const hook = safeWebhookClient(webhookUrl);
    if (!hook) {
      console.log(`[${server.SERVER_SHORTNAME}] Failed to create webhook client`);
      return false;
    }
    
    if (embeds) {
      hook.send({ content, embeds })
        .then(() => console.log(`[${server.SERVER_SHORTNAME}] Webhook message sent successfully`))
        .catch(err => console.error(`[${server.SERVER_SHORTNAME}] Error sending webhook message:`, err.message));
    } else {
      hook.send(content)
        .then(() => console.log(`[${server.SERVER_SHORTNAME}] Webhook message sent successfully`))
        .catch(err => console.error(`[${server.SERVER_SHORTNAME}] Error sending webhook message:`, err.message));
    }
    
    return true;
  } catch (err) {
    console.error(`[${server.SERVER_SHORTNAME}] Error in sendWebhookMessage:`, err.message);
    return false;
  }
}

// Add a debug wrapper for the rustrcon library
class DebugRcon {
  constructor(options) {
    console.log(`Creating RCON connection to ${options.ip}:${options.port}`);
    this.rcon = new rustrcon.Client(options);
    this.ip = options.ip;
    this.port = options.port;
    
    // Add event listeners with debug logging
    this.rcon.on('connected', () => {
      console.log(`[RCON DEBUG] Connected to ${this.ip}:${this.port}`);
      this.emit('connected');
    });
    
    this.rcon.on('disconnect', () => {
      console.log(`[RCON DEBUG] Disconnected from ${this.ip}:${this.port}`);
      this.emit('disconnect');
    });
    
    this.rcon.on('error', (err) => {
      console.log(`[RCON DEBUG] Error from ${this.ip}:${this.port}:`, err);
      this.emit('error', err);
    });
    
    this.rcon.on('message', (msg) => {
      console.log(`[RCON DEBUG] Message from ${this.ip}:${this.port}:`, 
        typeof msg.content === 'object' ? JSON.stringify(msg.content).substring(0, 100) + '...' : msg.content.substring(0, 100) + '...');
      this.emit('message', msg);
    });
    
    // Store event listeners
    this.events = {};
  }
  
  // Event handling
  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }
  
  emit(event, ...args) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(...args));
    }
  }
  
  // Forward methods to the underlying rcon client
  login() {
    console.log(`[RCON DEBUG] Attempting login to ${this.ip}:${this.port}`);
    return this.rcon.login();
  }
  
  disconnect() {
    console.log(`[RCON DEBUG] Manually disconnecting from ${this.ip}:${this.port}`);
    return this.rcon.disconnect();
  }
  
  send(command, identifier, messageIdentifier) {
    console.log(`[RCON DEBUG] Sending command to ${this.ip}:${this.port}: ${command}`);
    return this.rcon.send(command, identifier, messageIdentifier);
  }
  
  // Access to the underlying websocket
  get ws() {
    return this.rcon.ws;
  }
}

function debugLog(message) {
  console.log(`[DEBUG] ${message}`);
}

// Add a function to test webhooks
function testWebhook(webhookUrl, message) {
  if (!webhookUrl) {
    console.log("No webhook URL provided for testing");
    return false;
  }
  
  try {
    console.log(`Testing webhook: ${webhookUrl.substring(0, 30)}...`);
    const hook = new discord.WebhookClient({ url: webhookUrl });
    
    hook.send(message || "Webhook test message")
      .then(() => console.log("Webhook test successful"))
      .catch(err => console.error("Webhook test failed:", err));
      
    return true;
  } catch (err) {
    console.error("Error creating webhook client:", err);
    return false;
  }
}

// Add a function to send messages to Discord with better error handling
async function sendToDiscord(webhookUrl, content, embeds = null) {
  if (!webhookUrl) {
    console.log("No webhook URL provided");
    return false;
  }
  
  try {
    // Simple fix for common webhook URL issues
    let fixedUrl = webhookUrl.trim();
    
    const hook = new discord.WebhookClient({ url: fixedUrl });
    
    if (embeds) {
      await hook.send({ content, embeds });
    } else {
      await hook.send(content);
    }
    
    return true;
  } catch (err) {
    console.error("Error sending to Discord:", err);
    
    // If this is the 2x server, try to recover by skipping webhook messages
    if (server && server.SERVER_SHORTNAME && server.SERVER_SHORTNAME.includes("2x")) {
      console.log(`Skipping webhook message for ${server.SERVER_SHORTNAME} due to error`);
      return true; // Return true to prevent further error handling
    }
    
    return false;
  }
}

// Initialize variables
const servers = [];
const specialIdArray = [];
const botTokenArray = [];
const globalStatus = [];
const awaitingResponses = [];

// Create necessary directories
const directories = [
  './src/commands',
  './src/database',
  './src/images/playerprofiler',
  './src/images/stats',
  './src/images/leaderboard',
  './src/images/imagestorage',
  './src/images/serverstatus',
  './CONFIGS/SERVERS'
];

directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
  }
});

// Initialize database
let db = new sqlite3.Database('./src/database/database.sqlite3', (err) => {
    if(err) return console.log(err);
    
    // Create tables if they don't exist
    db.serialize(() => {
        db.run(`CREATE TABLE IF NOT EXISTS player_info (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            steam_id TEXT,
            server_id TEXT,
            name TEXT,
            picture TEXT,
            profile_url TEXT,
            connections INTEGER DEFAULT 0,
            kills INTEGER DEFAULT 0,
            deaths INTEGER DEFAULT 0,
            wipe_kills INTEGER DEFAULT 0,
            wipe_deaths INTEGER DEFAULT 0,
            chat_messages INTEGER DEFAULT 0,
            checker_whitelisted BOOLEAN DEFAULT 0,
            lastUpdated INTEGER
        )`);
        
        db.run(`CREATE TABLE IF NOT EXISTS server_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            server_id TEXT,
            current_players INTEGER,
            peak_players INTEGER,
            last_wipe INTEGER,
            joining_players INTEGER DEFAULT 0,
            queued_players INTEGER DEFAULT 0
        )`);
        
        db.run(`CREATE TABLE IF NOT EXISTS server_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            server_id TEXT,
            channel_id TEXT,
            message_id TEXT
        )`);
    });
});

// Read server configurations
if (fs.existsSync(serversDir)) {
  console.log(`Checking for server configs in: ${serversDir}`);
  const serverFiles = fs.readdirSync(serversDir).filter(file => file.endsWith('.json'));
  console.log(`Found ${serverFiles.length} server config files:`, serverFiles);
  
  serverFiles.forEach(file => {
    try {
      const serverFilePath = path.join(serversDir, file);
      console.log(`Loading server config from: ${serverFilePath}`);
      
      // Read the file directly instead of using require
      const serverContents = JSON.parse(fs.readFileSync(serverFilePath, 'utf8'));
      
      if (!serverContents.SERVER_ENABLED) {
        console.log(`Not loading ${serverContents.SERVER_SHORTNAME || file} because it is disabled`);
        return;
      }
      
      if (!serverContents.BOT_TOKEN) {
        console.log(`You have not provided a bot token for server (${serverContents.SERVER_SHORTNAME})`);
        return;
      }
      
      if (!serverContents.SERVER_IP) {
        console.log(`You have not provided a server IP for server (${serverContents.SERVER_SHORTNAME})`);
        return;
      }
      
      if (!serverContents.RCON_PASS) {
        console.log(`You have not provided a server Password for server (${serverContents.SERVER_SHORTNAME})`);
        return;
      }
      
      if (!serverContents.RCON_PORT) {
        console.log(`You have not provided a server Port for server (${serverContents.SERVER_SHORTNAME})`);
        return;
      }
      
      if (specialIdArray.includes(serverContents.SERVER_SPECIAL_ID)) {
        console.log(`Not loading ${serverContents.SERVER_SHORTNAME} because it shares the same server special ID's as another one of your servers`);
        return;
      }
      
      if (botTokenArray.includes(serverContents.BOT_TOKEN)) {
        console.log(`Not loading ${serverContents.SERVER_SHORTNAME} because it shares the same bot token as another one of your servers`);
        return;
      }
      
      specialIdArray.push(serverContents.SERVER_SPECIAL_ID);
      botTokenArray.push(serverContents.BOT_TOKEN);
      servers.push(serverContents);
      console.log(`Successfully loaded server config for: ${serverContents.SERVER_SHORTNAME}`);
      
    } catch (error) {
      console.error(`Error loading server config ${file}:`, error.message);
    }
  });
} else {
  console.log(`Servers directory not found: ${serversDir}`);
}

console.log(`Loaded ${servers.length} server configurations`);

// Start server connections
if (servers.length > 0) {
    console.log("Setting up server connections...");
    
    // Create server connections
    servers.forEach((server, index) => {
        console.log(`Setting up connection for ${server.SERVER_SHORTNAME}...`);
        
        // Initialize Discord client
        const client = new discord.Client({
            intents: [
                discord.GatewayIntentBits.Guilds,
                discord.GatewayIntentBits.GuildMessages,
                discord.GatewayIntentBits.MessageContent
            ]
        });
        
        // Set up RCON connection
        let rconIp = server.SERVER_IP;
        if (server.SERVER_IP.includes(":")) {
            rconIp = rconIp.split(":")[0];
        }
        
        console.log(`Creating RCON connection to ${rconIp}:${server.RCON_PORT}`);
        let rcon = new DebugRcon({
            ip: rconIp,
            port: server.RCON_PORT,
            password: server.RCON_PASS
        });
        
        server.connected = false;
        server.checkServerStatus = null;
        
        // Set up RCON event handlers
        rcon.on('connected', () => {
            console.log(`💚 Successfully connected to ${chalk.green(server.SERVER_SHORTNAME)}`);
            
            server.connected = true;
            
            // Send online notification
            if (server.SERVER_ONLINE_OFFLINE && server.SERVER_ONLINE_OFFLINE.ENABLED) {
                console.log(`Sending online notification for ${server.SERVER_SHORTNAME}`);
                handleOnlineMessage(server);
            }
            
            // Set up status check interval
            server.checkServerStatus = setInterval(() => {
                try {
                    rcon.send("serverinfo", "advrcon", 101);
                } catch(err) {
                    console.log(`Error sending serverinfo command: ${err.message}`);
                }
            }, 10000);
        });
        
        rcon.on('disconnect', () => {
            if (server.checkServerStatus) {
                clearInterval(server.checkServerStatus);
            }
            
            if (server.connected) {
                server.connected = false;
                
                // Send offline notification
                if (server.SERVER_ONLINE_OFFLINE && server.SERVER_ONLINE_OFFLINE.ENABLED) {
                    console.log(`Sending offline notification for ${server.SERVER_SHORTNAME}`);
                    handleOfflineMessage(server);
                }
                
                console.log(`❤️ Dropped connection to ${chalk.red(server.SERVER_SHORTNAME)}`);
            } else {
                console.log(`💛 Failed to connect to ${chalk.yellow(server.SERVER_SHORTNAME)}`);
            }
            
            // Update bot status
            client.user.setPresence({ 
                activities: [{ name: 'Server Offline', type: discord.ActivityType.Watching }], 
                status: 'dnd' 
            });
            
            // Try to reconnect after 30 seconds
            setTimeout(() => {
                console.log(`💜 Attempting a connection to ${chalk.magenta(server.SERVER_SHORTNAME)}`);
                rcon.login();
            }, 30000);
        });
        
        rcon.on('error', err => {
            console.log(`❤️ Error connecting to ${chalk.red(server.SERVER_SHORTNAME)}:`, err.message);
        });
        
        // Handle RCON messages
        rcon.on('message', async(message) => {
            try {
                let messageContent = message.content;
                let messageIdentifier = message.Identifier;
                
                // Handle server info messages
                if (messageIdentifier === 101) {
                    if (typeof messageContent === 'object') {
                        let popObject = { 
                            playersOnline: messageContent.Players || 0, 
                            maxPlayers: messageContent.MaxPlayers || 0, 
                            queuedPlayers: messageContent.Queued || 0, 
                            joiningPlayers: messageContent.Joining || 0 
                        };
                        
                        // Update global status
                        globalStatus[index] = popObject;
                        
                        // Update bot status if enabled
                        if (server.USE_POP_AS_A_BOT_STATUS && server.USE_POP_AS_A_BOT_STATUS.ENABLED) {
                            let statusMessage = `${popObject.playersOnline}/${popObject.maxPlayers} players`;
                            client.user.setPresence({ 
                                activities: [{ name: statusMessage, type: discord.ActivityType.Watching }], 
                                status: 'online' 
                            });
                        }
                    }
                }
                // Handle chat messages
                else if (messageIdentifier === 0 || !messageIdentifier) {
                    // Check if it's a chat message
                    if (typeof messageContent === 'string') {
                        // Try to extract player chat
                        const chatRegex = /\[([^\]]+)\] : (.*)/;
                        const match = messageContent.match(chatRegex);
                        
                        if (match && server.CHAT_LOGS && server.CHAT_LOGS.GLOBAL_CHAT_LOGS && 
                            server.CHAT_LOGS.GLOBAL_CHAT_LOGS.ENABLED && 
                            server.CHAT_LOGS.GLOBAL_CHAT_LOGS.GLOBAL_CHAT_WEBHOOK) {
                            
                            const username = match[1];
                            const msg = match[2];
                            
                            console.log(`Chat message from ${username}: ${msg}`);
                            
                            // Send to Discord
                            if (server.CHAT_LOGS.SIMPLE_FORMATTING) {
                                await sendToDiscord(
                                    server.CHAT_LOGS.GLOBAL_CHAT_LOGS.GLOBAL_CHAT_WEBHOOK,
                                    `**${username}**: ${msg}`
                                );
                            } else {
                                const embed = new discord.EmbedBuilder()
                                    .setDescription(msg)
                                    .setColor(server.CHAT_LOGS.GLOBAL_CHAT_LOGS.EMBED_COLOR || '#00ff26')
                                    .setAuthor({ name: username });
                                
                                await sendToDiscord(
                                    server.CHAT_LOGS.GLOBAL_CHAT_LOGS.GLOBAL_CHAT_WEBHOOK,
                                    null,
                                    [embed]
                                );
                            }
                        }
                    }
                }
            } catch (err) {
                console.error(`Error processing RCON message:`, err);
            }
        });
        
        // Set up Discord event handlers
        client.on('ready', () => {
            console.log(`(${chalk.magenta(index+1)}/${chalk.blue(servers.length)}) => 💚 [ ${chalk.green(client.user.tag)} ] is online!`);
            
            // Connect to RCON
            console.log(`💜 Attempting a connection to ${chalk.magenta(server.SERVER_SHORTNAME)}`);
            rcon.login();
        });
        
        client.on('messageCreate', async message => {
            if (message.author.bot) return;
            
            // Command to test all webhooks
            if (message.content === '!testwebhooks') {
                message.reply('Testing all configured webhooks...');
                
                let tested = 0;
                let success = 0;
                
                // Test chat webhook
                if (server.CHAT_LOGS?.GLOBAL_CHAT_LOGS?.ENABLED && 
                    server.CHAT_LOGS?.GLOBAL_CHAT_LOGS?.GLOBAL_CHAT_WEBHOOK) {
                    tested++;
                    const result = testWebhook(
                        server.CHAT_LOGS.GLOBAL_CHAT_LOGS.GLOBAL_CHAT_WEBHOOK,
                        `Chat webhook test from ${message.author.username}`
                    );
                    if (result) success++;
                }
                
                // Test online webhook
                if (server.SERVER_ONLINE_OFFLINE?.ENABLED && 
                    server.SERVER_ONLINE_OFFLINE?.ONLINE_EMBED_SETTINGS?.WEBHOOK) {
                    tested++;
                    const result = testWebhook(
                        server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.WEBHOOK,
                        `Online webhook test from ${message.author.username}`
                    );
                    if (result) success++;
                }
                
                // Test offline webhook
                if (server.SERVER_ONLINE_OFFLINE?.ENABLED && 
                    server.SERVER_ONLINE_OFFLINE?.OFFLINE_EMBED_SETTINGS?.WEBHOOK) {
                    tested++;
                    const result = testWebhook(
                        server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.WEBHOOK,
                        `Offline webhook test from ${message.author.username}`
                    );
                    if (result) success++;
                }
                
                message.reply(`Tested ${tested} webhooks, ${success} successful.`);
            }
            
            // Command to force online/offline notifications
            else if (message.content === '!forceonline') {
                message.reply('Forcing online notification...');
                handleOnlineMessage(server);
            }
            else if (message.content === '!forceoffline') {
                message.reply('Forcing offline notification...');
                handleOfflineMessage(server);
            }
            
            // Command to send a test chat message
            else if (message.content.startsWith('!fakechat')) {
                const fakeChatMessage = message.content.substring(10);
                if (!fakeChatMessage) {
                    return message.reply('Usage: !fakechat <message>');
                }
                
                message.reply('Sending fake chat message to Discord...');
                
                if (!server.CHAT_LOGS?.GLOBAL_CHAT_LOGS?.ENABLED || 
                    !server.CHAT_LOGS?.GLOBAL_CHAT_LOGS?.GLOBAL_CHAT_WEBHOOK) {
                    return message.reply('Chat logs are not enabled or webhook is not set.');
                }
                
                try {
                    if (server.CHAT_LOGS.SIMPLE_FORMATTING) {
                        await sendToDiscord(
                            server.CHAT_LOGS.GLOBAL_CHAT_LOGS.GLOBAL_CHAT_WEBHOOK,
                            `**TestPlayer**: ${fakeChatMessage}`
                        );
                    } else {
                        const embed = new discord.EmbedBuilder()
                            .setDescription(fakeChatMessage)
                            .setColor(server.CHAT_LOGS.GLOBAL_CHAT_LOGS.EMBED_COLOR || '#00ff26')
                            .setAuthor({ name: 'TestPlayer' });
                        
                        await sendToDiscord(
                            server.CHAT_LOGS.GLOBAL_CHAT_LOGS.GLOBAL_CHAT_WEBHOOK,
                            null,
                            [embed]
                        );
                    }
                    
                    message.reply('Fake chat message sent to Discord.');
                } catch (err) {
                    message.reply(`Error sending fake chat message: ${err.message}`);
                }
            }
            
            // Command to set chat webhook
            else if (message.content.startsWith('!setchat')) {
                const parts = message.content.split(' ');
                if (parts.length < 2) {
                    return message.reply('Usage: !setchat <webhook-url>');
                }
                
                const webhookUrl = parts[1];
                
                // Initialize CHAT_LOGS if needed
                if (!server.CHAT_LOGS) server.CHAT_LOGS = {};
                if (!server.CHAT_LOGS.GLOBAL_CHAT_LOGS) server.CHAT_LOGS.GLOBAL_CHAT_LOGS = {};
                
                // Update settings
                server.CHAT_LOGS.GLOBAL_CHAT_LOGS.ENABLED = true;
                server.CHAT_LOGS.GLOBAL_CHAT_LOGS.GLOBAL_CHAT_WEBHOOK = webhookUrl;
                server.CHAT_LOGS.SIMPLE_FORMATTING = true;
                
                message.reply(`Chat logs enabled with webhook: ${webhookUrl.substring(0, 20)}...`);
                
                // Test the webhook
                testWebhook(webhookUrl, `Chat logs enabled by ${message.author.username}`);
                
                // Save the configuration
                try {
                    const serverFilePath = path.join(serversDir, `${server.SERVER_SPECIAL_ID}.json`);
                    fs.writeFileSync(serverFilePath, JSON.stringify(server, null, 2));
                    message.reply(`Configuration saved to ${serverFilePath}`);
                } catch (err) {
                    message.reply(`Error saving configuration: ${err.message}`);
                }
            }
            
            // Command to set status webhook
            else if (message.content.startsWith('!setstatus')) {
                const parts = message.content.split(' ');
                if (parts.length < 2) {
                    return message.reply('Usage: !setstatus <webhook-url>');
                }
                
                const webhookUrl = parts[1];
                
                // Initialize SERVER_ONLINE_OFFLINE if needed
                if (!server.SERVER_ONLINE_OFFLINE) server.SERVER_ONLINE_OFFLINE = {};
                if (!server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS = {};
                if (!server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS = {};
                
                // Update settings
                server.SERVER_ONLINE_OFFLINE.ENABLED = true;
                server.SERVER_ONLINE_OFFLINE.SIMPLE_FORMATTING = true;
                server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.WEBHOOK = webhookUrl;
                server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.WEBHOOK = webhookUrl;
                
                message.reply(`Server status notifications enabled with webhook: ${webhookUrl.substring(0, 20)}...`);
                
                // Test the webhook
                testWebhook(webhookUrl, `Server status notifications enabled by ${message.author.username}`);
                
                // Save the configuration
                try {
                    const serverFilePath = path.join(serversDir, `${server.SERVER_SPECIAL_ID}.json`);
                    fs.writeFileSync(serverFilePath, JSON.stringify(server, null, 2));
                    message.reply(`Configuration saved to ${serverFilePath}`);
                } catch (err) {
                    message.reply(`Error saving configuration: ${err.message}`);
                }
            }
            
            // Command to check configuration
            else if (message.content === '!config') {
                let configInfo = `**Server Configuration for ${server.SERVER_SHORTNAME}**\n\n`;
                
                // Chat logs
                configInfo += `**Chat Logs:** ${server.CHAT_LOGS?.GLOBAL_CHAT_LOGS?.ENABLED ? '✅ Enabled' : '❌ Disabled'}\n`;
                if (server.CHAT_LOGS?.GLOBAL_CHAT_LOGS?.ENABLED) {
                    configInfo += `Webhook: ${server.CHAT_LOGS.GLOBAL_CHAT_LOGS.GLOBAL_CHAT_WEBHOOK ? '✅ Set' : '❌ Not Set'}\n`;
                    configInfo += `Format: ${server.CHAT_LOGS.SIMPLE_FORMATTING ? 'Simple' : 'Embed'}\n`;
                }
                
                // Server status
                configInfo += `\n**Server Status:** ${server.SERVER_ONLINE_OFFLINE?.ENABLED ? '✅ Enabled' : '❌ Disabled'}\n`;
                if (server.SERVER_ONLINE_OFFLINE?.ENABLED) {
                    configInfo += `Online Webhook: ${server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS?.WEBHOOK ? '✅ Set' : '❌ Not Set'}\n`;
                    configInfo += `Offline Webhook: ${server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS?.WEBHOOK ? '✅ Set' : '❌ Not Set'}\n`;
                    configInfo += `Format: ${server.SERVER_ONLINE_OFFLINE.SIMPLE_FORMATTING ? 'Simple' : 'Embed'}\n`;
                }
                
                // RCON status
                configInfo += `\n**RCON Connection:** ${server.connected ? '✅ Connected' : '❌ Disconnected'}\n`;
                configInfo += `Server IP: ${server.SERVER_IP}\n`;
                configInfo += `RCON Port: ${server.RCON_PORT}\n`;
                
                message.reply(configInfo);
            }
            
            // Command to test RCON connection
            else if (message.content === '!test') {
                message.reply('Testing RCON connection...');
                
                if (!server.connected) {
                    message.reply('Not connected to RCON. Attempting to connect...');
                    rcon.login();
                    return;
                }
                
                try {
                    rcon.send("status", "test", 999);
                    message.reply('RCON command sent. Check console for response.');
                } catch (err) {
                    message.reply(`Error sending RCON command: ${err.message}`);
                }
            }
            
            // Command to reconnect to RCON
            else if (message.content === '!reconnect') {
                message.reply('Reconnecting to RCON...');
                
                // Disconnect if connected
                if (rcon.ws && rcon.ws.readyState === 1) {
                    rcon.disconnect();
                }
                
                // Connect to RCON
                setTimeout(() => {
                    rcon.login();
                    message.reply('RCON reconnection initiated.');
                }, 1000);
            }
        });
        
        // Add a command to regenerate the webhook URL
        client.on('messageCreate', async message => {
            if (message.author.bot) return;
            
            // Command to regenerate webhook URL
            if (message.content === '!regeneratewebhook') {
                if (!server.SERVER_SHORTNAME.includes("2x")) {
                    return message.reply('This command is only for the 2x server.');
                }
                
                message.reply('Please send the new webhook URL for the 2x server:');
                
                // Set up a collector to wait for the webhook URL
                const filter = m => m.author.id === message.author.id;
                const collector = message.channel.createMessageCollector({ filter, time: 60000, max: 1 });
                
                collector.on('collect', async m => {
                    const newWebhookUrl = m.content.trim();
                    
                    if (!newWebhookUrl.startsWith('https://discord.com/api/webhooks/')) {
                        return message.reply('Invalid webhook URL. It should start with https://discord.com/api/webhooks/');
                    }
                    
                    // Update the webhook URLs
                    if (!server.SERVER_ONLINE_OFFLINE) server.SERVER_ONLINE_OFFLINE = {};
                    if (!server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS = {};
                    if (!server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS = {};
                    
                    server.SERVER_ONLINE_OFFLINE.ENABLED = true;
                    server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.WEBHOOK = newWebhookUrl;
                    server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.WEBHOOK = newWebhookUrl;
                    
                    // Save the configuration
                    try {
                        const serverFilePath = path.join(serversDir, `${server.SERVER_SPECIAL_ID}.json`);
                        fs.writeFileSync(serverFilePath, JSON.stringify(server, null, 2));
                        message.reply(`Updated webhook URL and saved to ${serverFilePath}`);
                        
                        // Test the webhook
                        try {
                            const hook = new discord.WebhookClient({ url: newWebhookUrl });
                            await hook.send(`Webhook updated by ${message.author.username}`);
                            message.reply('Webhook test successful!');
                        } catch (err) {
                            message.reply(`Webhook test failed: ${err.message}`);
                        }
                    } catch (err) {
                        message.reply(`Error saving configuration: ${err.message}`);
                    }
                });
                
                collector.on('end', collected => {
                    if (collected.size === 0) {
                        message.reply('Command timed out. Please try again.');
                    }
                });
            }
        });
        
        // Add a command to check and fix webhook URLs
        client.on('messageCreate', async message => {
            if (message.author.bot) return;
            
            // Command to check webhook URLs
            if (message.content === '!checkwebhooks') {
                let reply = `**Webhook Status for ${server.SERVER_SHORTNAME}**\n\n`;
                
                if (!server.SERVER_ONLINE_OFFLINE || !server.SERVER_ONLINE_OFFLINE.ENABLED) {
                    reply += `Server online/offline notifications not enabled\n`;
                } else {
                    const onlineWebhook = server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS?.WEBHOOK;
                    const offlineWebhook = server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS?.WEBHOOK;
                    
                    reply += `**Online webhook:** ${onlineWebhook ? 'Set' : 'Not set'}\n`;
                    reply += `**Offline webhook:** ${offlineWebhook ? 'Set' : 'Not set'}\n\n`;
                    
                    if (onlineWebhook) {
                        const validOnline = fixWebhookUrl(onlineWebhook);
                        reply += `**Online webhook valid:** ${validOnline ? 'Yes' : 'No'}\n`;
                        
                        if (!validOnline) {
                            reply += `**Issue:** URL does not start with https://discord.com/api/webhooks/\n`;
                        }
                    }
                    
                    if (offlineWebhook) {
                        const validOffline = fixWebhookUrl(offlineWebhook);
                        reply += `**Offline webhook valid:** ${validOffline ? 'Yes' : 'No'}\n`;
                        
                        if (!validOffline) {
                            reply += `**Issue:** URL does not start with https://discord.com/api/webhooks/\n`;
                        }
                    }
                }
                
                message.reply(reply);
            }
            
            // Command to update webhook URLs
            else if (message.content.startsWith('!updatewebhook')) {
                const parts = message.content.split(' ');
                if (parts.length < 2) {
                    return message.reply('Usage: !updatewebhook <webhook-url>');
                }
                
                const webhookUrl = parts.slice(1).join(' ');
                const validUrl = fixWebhookUrl(webhookUrl);
                
                if (!validUrl) {
                    return message.reply('Invalid webhook URL. It must start with https://discord.com/api/webhooks/');
                }
                
                // Update the webhook URLs
                if (!server.SERVER_ONLINE_OFFLINE) server.SERVER_ONLINE_OFFLINE = {};
                if (!server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS = {};
                if (!server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS = {};
                
                server.SERVER_ONLINE_OFFLINE.ENABLED = true;
                server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.WEBHOOK = validUrl;
                server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.WEBHOOK = validUrl;
                
                // Save the configuration
                try {
                    const serverFilePath = path.join(serversDir, `${server.SERVER_SPECIAL_ID}.json`);
                    fs.writeFileSync(serverFilePath, JSON.stringify(server, null, 2));
                    message.reply(`Updated webhook URL and saved to ${serverFilePath}`);
                    
                    // Test the webhook
                    try {
                        const hook = safeWebhookClient(validUrl);
                        await hook.send(`Webhook updated by ${message.author.username}`);
                        message.reply('Webhook test successful!');
                    } catch (err) {
                        message.reply(`Webhook test failed: ${err.message}`);
                    }
                } catch (err) {
                    message.reply(`Error saving configuration: ${err.message}`);
                }
            }
            
            // Command to test webhook
            else if (message.content === '!testwebhook') {
                if (!server.SERVER_ONLINE_OFFLINE || !server.SERVER_ONLINE_OFFLINE.ENABLED) {
                    return message.reply('Server online/offline notifications not enabled');
                }
                
                const webhookUrl = server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS?.WEBHOOK;
                if (!webhookUrl) {
                    return message.reply('No webhook URL set for online notifications');
                }
                
                const validUrl = fixWebhookUrl(webhookUrl);
                if (!validUrl) {
                    return message.reply('Invalid webhook URL. It must start with https://discord.com/api/webhooks/');
                }
                
                try {
                    const hook = safeWebhookClient(validUrl);
                    await hook.send(`Webhook test from ${message.author.username}`);
                    message.reply('Webhook test successful!');
                } catch (err) {
                    message.reply(`Webhook test failed: ${err.message}`);
                }
            }
        });
        
        // Add a simple command to fix the 2x server webhook
        client.on('messageCreate', async message => {
            if (message.author.bot) return;
            
            // Command to fix the 2x server webhook
            if (message.content === '!fix2xwebhook' && server.SERVER_SHORTNAME.includes('2x')) {
                message.reply(`Please send the new webhook URL for ${server.SERVER_SHORTNAME}:`);
                
                const filter = m => m.author.id === message.author.id;
                const collector = message.channel.createMessageCollector({ filter, time: 60000, max: 1 });
                
                collector.on('collect', async m => {
                    const newWebhookUrl = m.content.trim();
                    
                    // Update the webhook URLs
                    if (!server.SERVER_ONLINE_OFFLINE) server.SERVER_ONLINE_OFFLINE = {};
                    if (!server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS = {};
                    if (!server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS = {};
                    
                    server.SERVER_ONLINE_OFFLINE.ENABLED = true;
                    server.SERVER_ONLINE_OFFLINE.SIMPLE_FORMATTING = true;
                    server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.WEBHOOK = newWebhookUrl;
                    server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.WEBHOOK = newWebhookUrl;
                    
                    message.reply(`Updated webhook URL for ${server.SERVER_SHORTNAME}`);
                    
                    // Test the webhook
                    try {
                        const hook = new discord.WebhookClient({ url: newWebhookUrl });
                        await hook.send(`Test message from ${message.author.username}`);
                        message.reply('Webhook test successful!');
                    } catch (err) {
                        message.reply(`Error testing webhook: ${err.message}`);
                    }
                });
            }
        });
        
        // Add a command to regenerate the webhook URL
        client.on('messageCreate', async message => {
            if (message.author.bot) return;
            
            // Command to regenerate webhook URL
            if (message.content === '!regeneratewebhook') {
                if (!server.SERVER_SHORTNAME.includes("2x")) {
                    return message.reply('This command is only for the 2x server.');
                }
                
                message.reply('Please send the new webhook URL for the 2x server:');
                
                // Set up a collector to wait for the webhook URL
                const filter = m => m.author.id === message.author.id;
                const collector = message.channel.createMessageCollector({ filter, time: 60000, max: 1 });
                
                collector.on('collect', async m => {
                    const newWebhookUrl = m.content.trim();
                    
                    if (!newWebhookUrl.startsWith('https://discord.com/api/webhooks/')) {
                        return message.reply('Invalid webhook URL. It should start with https://discord.com/api/webhooks/');
                    }
                    
                    // Update the webhook URLs
                    if (!server.SERVER_ONLINE_OFFLINE) server.SERVER_ONLINE_OFFLINE = {};
                    if (!server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS = {};
                    if (!server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS = {};
                    
                    server.SERVER_ONLINE_OFFLINE.ENABLED = true;
                    server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.WEBHOOK = newWebhookUrl;
                    server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.WEBHOOK = newWebhookUrl;
                    
                    // Save the configuration
                    try {
                        const serverFilePath = path.join(serversDir, `${server.SERVER_SPECIAL_ID}.json`);
                        fs.writeFileSync(serverFilePath, JSON.stringify(server, null, 2));
                        message.reply(`Updated webhook URL and saved to ${serverFilePath}`);
                        
                        // Test the webhook
                        try {
                            const hook = new discord.WebhookClient({ url: newWebhookUrl });
                            await hook.send(`Webhook updated by ${message.author.username}`);
                            message.reply('Webhook test successful!');
                        } catch (err) {
                            message.reply(`Webhook test failed: ${err.message}`);
                        }
                    } catch (err) {
                        message.reply(`Error saving configuration: ${err.message}`);
                    }
                });
                
                collector.on('end', collected => {
                    if (collected.size === 0) {
                        message.reply('Command timed out. Please try again.');
                    }
                });
            }
        });
        
        // Add a command to check and fix webhook URLs
        client.on('messageCreate', async message => {
            if (message.author.bot) return;
            
            // Command to check webhook URLs
            if (message.content === '!checkwebhooks') {
                let reply = `**Webhook Status for ${server.SERVER_SHORTNAME}**\n\n`;
                
                if (!server.SERVER_ONLINE_OFFLINE || !server.SERVER_ONLINE_OFFLINE.ENABLED) {
                    reply += `Server online/offline notifications not enabled\n`;
                } else {
                    const onlineWebhook = server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS?.WEBHOOK;
                    const offlineWebhook = server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS?.WEBHOOK;
                    
                    reply += `**Online webhook:** ${onlineWebhook ? 'Set' : 'Not set'}\n`;
                    reply += `**Offline webhook:** ${offlineWebhook ? 'Set' : 'Not set'}\n\n`;
                    
                    if (onlineWebhook) {
                        const validOnline = fixWebhookUrl(onlineWebhook);
                        reply += `**Online webhook valid:** ${validOnline ? 'Yes' : 'No'}\n`;
                        
                        if (!validOnline) {
                            reply += `**Issue:** URL does not start with https://discord.com/api/webhooks/\n`;
                        }
                    }
                    
                    if (offlineWebhook) {
                        const validOffline = fixWebhookUrl(offlineWebhook);
                        reply += `**Offline webhook valid:** ${validOffline ? 'Yes' : 'No'}\n`;
                        
                        if (!validOffline) {
                            reply += `**Issue:** URL does not start with https://discord.com/api/webhooks/\n`;
                        }
                    }
                }
                
                message.reply(reply);
            }
            
            // Command to update webhook URLs
            else if (message.content.startsWith('!updatewebhook')) {
                const parts = message.content.split(' ');
                if (parts.length < 2) {
                    return message.reply('Usage: !updatewebhook <webhook-url>');
                }
                
                const webhookUrl = parts.slice(1).join(' ');
                const validUrl = fixWebhookUrl(webhookUrl);
                
                if (!validUrl) {
                    return message.reply('Invalid webhook URL. It must start with https://discord.com/api/webhooks/');
                }
                
                // Update the webhook URLs
                if (!server.SERVER_ONLINE_OFFLINE) server.SERVER_ONLINE_OFFLINE = {};
                if (!server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS = {};
                if (!server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS = {};
                
                server.SERVER_ONLINE_OFFLINE.ENABLED = true;
                server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.WEBHOOK = validUrl;
                server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.WEBHOOK = validUrl;
                
                // Save the configuration
                try {
                    const serverFilePath = path.join(serversDir, `${server.SERVER_SPECIAL_ID}.json`);
                    fs.writeFileSync(serverFilePath, JSON.stringify(server, null, 2));
                    message.reply(`Updated webhook URL and saved to ${serverFilePath}`);
                    
                    // Test the webhook
                    try {
                        const hook = safeWebhookClient(validUrl);
                        await hook.send(`Webhook updated by ${message.author.username}`);
                        message.reply('Webhook test successful!');
                    } catch (err) {
                        message.reply(`Webhook test failed: ${err.message}`);
                    }
                } catch (err) {
                    message.reply(`Error saving configuration: ${err.message}`);
                }
            }
            
            // Command to test webhook
            else if (message.content === '!testwebhook') {
                if (!server.SERVER_ONLINE_OFFLINE || !server.SERVER_ONLINE_OFFLINE.ENABLED) {
                    return message.reply('Server online/offline notifications not enabled');
                }
                
                const webhookUrl = server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS?.WEBHOOK;
                if (!webhookUrl) {
                    return message.reply('No webhook URL set for online notifications');
                }
                
                const validUrl = fixWebhookUrl(webhookUrl);
                if (!validUrl) {
                    return message.reply('Invalid webhook URL. It must start with https://discord.com/api/webhooks/');
                }
                
                try {
                    const hook = safeWebhookClient(validUrl);
                    await hook.send(`Webhook test from ${message.author.username}`);
                    message.reply('Webhook test successful!');
                } catch (err) {
                    message.reply(`Webhook test failed: ${err.message}`);
                }
            }
        });
        
        // Add a command to fix the 2x server webhook
        client.on('messageCreate', async message => {
            if (message.author.bot) return;
            
            // Command to fix the 2x server webhook
            if (message.content === '!fix2xwebhook') {
                if (!server.SERVER_SHORTNAME.includes("2x")) {
                    return message.reply('This command is only for the 2x server.');
                }
                
                message.reply('Please send the new webhook URL for the 2x server:');
                
                // Set up a collector to wait for the webhook URL
                const filter = m => m.author.id === message.author.id;
                const collector = message.channel.createMessageCollector({ filter, time: 60000, max: 1 });
                
                collector.on('collect', async m => {
                    const newWebhookUrl = m.content.trim();
                    
                    if (!newWebhookUrl.startsWith('https://discord.com/api/webhooks/')) {
                        return message.reply('Invalid webhook URL. It should start with https://discord.com/api/webhooks/');
                    }
                    
                    // Update the webhook URLs
                    if (!server.SERVER_ONLINE_OFFLINE) server.SERVER_ONLINE_OFFLINE = {};
                    if (!server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS = {};
                    if (!server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS = {};
                    
                    server.SERVER_ONLINE_OFFLINE.ENABLED = true;
                    server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.WEBHOOK = newWebhookUrl;
                    server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.WEBHOOK = newWebhookUrl;
                    
                    // Save the configuration
                    try {
                        const serverFilePath = path.join(serversDir, `${server.SERVER_SPECIAL_ID}.json`);
                        fs.writeFileSync(serverFilePath, JSON.stringify(server, null, 2));
                        message.reply(`Updated webhook URL and saved to ${serverFilePath}`);
                        
                        // Test the webhook
                        const hook = safeWebhookClient(newWebhookUrl);
                        if (hook) {
                            try {
                                await hook.send(`Webhook updated by ${message.author.username}`);
                                message.reply('Webhook test successful!');
                            } catch (err) {
                                message.reply(`Webhook test failed: ${err.message}`);
                            }
                        } else {
                            message.reply('Failed to create webhook client with the new URL.');
                        }
                    } catch (err) {
                        message.reply(`Error saving configuration: ${err.message}`);
                    }
                });
                
                collector.on('end', collected => {
                    if (collected.size === 0) {
                        message.reply('Command timed out. Please try again.');
                    }
                });
            }
        });
        
        // Add a command to fix the webhook URL for this specific server
        client.on('messageCreate', async message => {
            if (message.author.bot) return;
            
            const fixCommand = `!fix${server.SERVER_SHORTNAME.replace(/\s+/g, '')}webhook`;
            if (message.content === fixCommand) {
                message.reply(`Current webhook for ${server.SERVER_SHORTNAME}: ${server.SERVER_ONLINE_OFFLINE?.ONLINE_EMBED_SETTINGS?.WEBHOOK || 'Not set'}\n\nPlease send the new webhook URL:`);
                
                const filter = m => m.author.id === message.author.id;
                const collector = message.channel.createMessageCollector({ filter, time: 60000, max: 1 });
                
                collector.on('collect', async m => {
                    const newWebhookUrl = m.content.trim();
                    
                    if (!validateWebhookUrl(newWebhookUrl)) {
                        return message.reply('Invalid webhook URL. It must start with https://discord.com/api/webhooks/');
                    }
                    
                    // Test the webhook
                    try {
                        const hook = new discord.WebhookClient({ url: newWebhookUrl });
                        await hook.send(`Test message from ${message.author.username}`);
                        
                        // Update the webhook URLs
                        if (!server.SERVER_ONLINE_OFFLINE) server.SERVER_ONLINE_OFFLINE = {};
                        if (!server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS = {};
                        if (!server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS = {};
                        
                        server.SERVER_ONLINE_OFFLINE.ENABLED = true;
                        server.SERVER_ONLINE_OFFLINE.SIMPLE_FORMATTING = true;
                        server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.WEBHOOK = newWebhookUrl;
                        server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.WEBHOOK = newWebhookUrl;
                        
                        // Save the configuration
                        try {
                            const serverFilePath = path.join(serversDir, `${server.SERVER_SPECIAL_ID}.json`);
                            fs.writeFileSync(serverFilePath, JSON.stringify(server, null, 2));
                            message.reply(`Updated webhook URL for ${server.SERVER_SHORTNAME} and saved to ${serverFilePath}`);
                            
                            // Send a test notification
                            handleOnlineMessage(server);
                        } catch (err) {
                            message.reply(`Error saving configuration: ${err.message}`);
                        }
                    } catch (err) {
                        message.reply(`Error testing webhook: ${err.message}\nPlease make sure the URL is valid and try again.`);
                    }
                });
                
                collector.on('end', collected => {
                    if (collected.size === 0) {
                        message.reply('Command timed out. Please try again.');
                    }
                });
            }
            
            // Command to test notifications
            if (message.content === '!testnotifications') {
                message.reply(`Testing notifications for ${server.SERVER_SHORTNAME}...`);
                handleOnlineMessage(server);
                message.reply(`Test complete. Check your webhook channel.`);
            }
        });
        
        // Add this to the client.on('messageCreate') handler
        client.on('messageCreate', async message => {
            if (message.author.bot) return;
            
            // Command to fix the 2x server webhook
            if (message.content === `!fix${server.SERVER_SHORTNAME.replace(/\s+/g, '')}webhook`) {
                message.reply(`Current webhook for ${server.SERVER_SHORTNAME}: ${server.SERVER_ONLINE_OFFLINE?.ONLINE_EMBED_SETTINGS?.WEBHOOK || 'Not set'}\n\nPlease send the new webhook URL:`);
                
                const filter = m => m.author.id === message.author.id;
                const collector = message.channel.createMessageCollector({ filter, time: 60000, max: 1 });
                
                collector.on('collect', async m => {
                    const newWebhookUrl = m.content.trim();
                    
                    // Validate the webhook URL
                    if (!newWebhookUrl.startsWith('https://discord.com/api/webhooks/')) {
                        return message.reply('Invalid webhook URL. It must start with https://discord.com/api/webhooks/');
                    }
                    
                    // Test the webhook
                    try {
                        const hook = new discord.WebhookClient({ url: newWebhookUrl });
                        await hook.send(`Test message from ${message.author.username}`);
                        
                        // Update the webhook URLs
                        if (!server.SERVER_ONLINE_OFFLINE) server.SERVER_ONLINE_OFFLINE = {};
                        if (!server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS = {};
                        if (!server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS) server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS = {};
                        
                        server.SERVER_ONLINE_OFFLINE.ENABLED = true;
                        server.SERVER_ONLINE_OFFLINE.SIMPLE_FORMATTING = true;
                        server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.WEBHOOK = newWebhookUrl;
                        server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.WEBHOOK = newWebhookUrl;
                        
                        // Save the configuration
                        try {
                            const serverFilePath = path.join(serversDir, `${server.SERVER_SPECIAL_ID}.json`);
                            fs.writeFileSync(serverFilePath, JSON.stringify(server, null, 2));
                            message.reply(`Updated webhook URL for ${server.SERVER_SHORTNAME} and saved to ${serverFilePath}`);
                        } catch (err) {
                            message.reply(`Error saving configuration: ${err.message}`);
                        }
                    } catch (err) {
                        message.reply(`Error testing webhook: ${err.message}\nPlease make sure the URL is valid and try again.`);
                    }
                });
                
                collector.on('end', collected => {
                    if (collected.size === 0) {
                        message.reply('Command timed out. Please try again.');
                    }
                });
            }
        });
        
        // Login to Discord
        client.login(server.BOT_TOKEN).catch(err => {
            console.error(`Failed to login to Discord for ${server.SERVER_SHORTNAME}:`, err);
        });
    });
} else {
    console.log("No enabled servers found. Please configure your server settings in the CONFIGS/SERVERS directory.");
}

// If global population bot is enabled, start it
if (servers.length > 0 && config.GLOBAL_POP_BOT && config.GLOBAL_POP_BOT.ENABLED) {
    console.log("Starting global population bot...");
    
    const totalPopulationBot = new discord.Client({
        intents: [discord.GatewayIntentBits.Guilds]
    });
    
    totalPopulationBot.once('ready', () => {
        console.log(`(${chalk.magenta("GLOBAL")}) => 💚 [ ${chalk.green(totalPopulationBot.user.tag)} ] is online... Proceeding...`);
        
        setInterval(() => {
            let totalPlayers = 0;
            let totalMaxPlayers = 0;
            let totalJoining = 0;
            let totalQueued = 0;
            
            servers.forEach(server => {
                if (server.connected && server.playerObject) {
                    totalPlayers += server.playerObject.playersOnline || 0;
                    totalMaxPlayers += server.playerObject.maxPlayers || 0;
                    totalJoining += server.playerObject.joiningPlayers || 0;
                    totalQueued += server.playerObject.queuedPlayers || 0;
                }
            });
            
            let popMessage = config.GLOBAL_POP_BOT.SINGLE_MESSAGE.PLAYER_COUNT_MESSAGE;
            popMessage = popMessage.replace(/{OnlineJoiningQueued}/gi, totalPlayers + totalJoining + totalQueued);
            popMessage = popMessage.replace(/{playersOnline}/gi, totalPlayers);
            popMessage = popMessage.replace(/{maxPlayers}/gi, totalMaxPlayers);
            popMessage = popMessage.replace(/{joiningPlayers}/gi, totalJoining);
            popMessage = popMessage.replace(/{queuedPlayers}/gi, totalQueued);
            
            console.log(`(${chalk.magenta("GLOBAL")}) => 👉 [ ${chalk.cyan(totalPopulationBot.user.tag)} ] status: ${chalk.cyan(popMessage)}`);  
            totalPopulationBot.user.setPresence({ activities: [{ name: popMessage, type: discord.ActivityType.Watching }], status: "online" });
        }, 10000);
    });
    
    totalPopulationBot.login(config.GLOBAL_POP_BOT.BOT_TOKEN).catch(err => {
        console.error("Failed to login to global population bot:", err);
    });
}

console.log("Ultimate RCON+ setup complete!");

// Export the variables
module.exports = {
  servers,
  specialIdArray,
  botTokenArray,
  globalStatus,
  awaitingResponses
};

// Add these helper functions for online/offline notifications
function handleOnlineMessage(server) {
    try {
        if (!server.SERVER_ONLINE_OFFLINE || !server.SERVER_ONLINE_OFFLINE.ENABLED) {
            console.log(`[${server.SERVER_SHORTNAME}] Server online/offline notifications not enabled`);
            return;
        }
        
        const webhookUrl = server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS?.WEBHOOK;
        if (!webhookUrl) {
            console.log(`[${server.SERVER_SHORTNAME}] No webhook URL set for online notifications`);
            return;
        }
        
        console.log(`Sending online notification for ${server.SERVER_SHORTNAME}`);
        
        // Use the fixWebhookUrl function to validate and fix the webhook URL
        const validUrl = fixWebhookUrl(webhookUrl);
        if (!validUrl) {
            console.error(`[${server.SERVER_SHORTNAME}] Invalid webhook URL`);
            logWebhookError(server, { message: "The provided webhook URL is not valid." });
            return;
        }
        
        console.log(`[${server.SERVER_SHORTNAME}] Sending online notification to webhook`);
        
        try {
            const hook = new discord.WebhookClient({ url: validUrl });
            
            if (server.SERVER_ONLINE_OFFLINE.SIMPLE_FORMATTING) {
                hook.send(`🖥️✅ **${server.SERVER_SHORTNAME}** has gone **online!**`)
                    .then(() => console.log(`[${server.SERVER_SHORTNAME}] Online notification sent successfully`))
                    .catch(err => {
                        console.error(`[${server.SERVER_SHORTNAME}] Error sending online notification: ${err.message}`);
                        logWebhookError(server, err);
                    });
            } else {
                const embed = new discord.EmbedBuilder()
                    .setTitle((server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.TITLE || '{SERVER_SHORTNAME} is online').replace(/{SERVER_SHORTNAME}/gi, server.SERVER_SHORTNAME))
                    .setDescription(server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.DESCRIPTION || '')
                    .setColor(server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.COLOR || '#49e637')
                    .setTimestamp();
                    
                if (server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.LARGE_IMAGE) {
                    embed.setImage(server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.LARGE_IMAGE);
                }
                
                if (server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.FOOTER) {
                    embed.setFooter({ text: server.SERVER_ONLINE_OFFLINE.ONLINE_EMBED_SETTINGS.FOOTER });
                }
                
                hook.send({ embeds: [embed] })
                    .then(() => console.log(`[${server.SERVER_SHORTNAME}] Online notification sent successfully`))
                    .catch(err => {
                        console.error(`[${server.SERVER_SHORTNAME}] Error sending online notification: ${err.message}`);
                        logWebhookError(server, err);
                    });
            }
        } catch (err) {
            console.error(`[${server.SERVER_SHORTNAME}] Error handling online message: ${err.message}`);
            logWebhookError(server, err);
        }
    } catch (err) {
        console.error(`[${server.SERVER_SHORTNAME}] Error handling online message: ${err.message}`);
    }
}

function handleOfflineMessage(server) {
    try {
        if (!server.SERVER_ONLINE_OFFLINE || !server.SERVER_ONLINE_OFFLINE.ENABLED) {
            console.log(`[${server.SERVER_SHORTNAME}] Server online/offline notifications not enabled`);
            return;
        }
        
        const webhookUrl = server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS?.WEBHOOK;
        if (!webhookUrl) {
            console.log(`[${server.SERVER_SHORTNAME}] No webhook URL set for offline notifications`);
            return;
        }
        
        // Use the fixWebhookUrl function to validate and fix the webhook URL
        const validUrl = fixWebhookUrl(webhookUrl);
        if (!validUrl) {
            console.error(`[${server.SERVER_SHORTNAME}] Invalid webhook URL`);
            logWebhookError(server, { message: "The provided webhook URL is not valid." });
            return;
        }
        
        console.log(`[${server.SERVER_SHORTNAME}] Sending offline notification to webhook`);
        
        try {
            const hook = new discord.WebhookClient({ url: validUrl });
            
            if (server.SERVER_ONLINE_OFFLINE.SIMPLE_FORMATTING) {
                hook.send(`🖥️❌ **${server.SERVER_SHORTNAME}** has gone **offline!**`)
                    .then(() => console.log(`[${server.SERVER_SHORTNAME}] Offline notification sent successfully`))
                    .catch(err => {
                        console.error(`[${server.SERVER_SHORTNAME}] Error sending offline notification: ${err.message}`);
                        logWebhookError(server, err);
                    });
            } else {
                const embed = new discord.EmbedBuilder()
                    .setTitle((server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.TITLE || '{SERVER_SHORTNAME} is offline').replace(/{SERVER_SHORTNAME}/gi, server.SERVER_SHORTNAME))
                    .setDescription(server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.DESCRIPTION || '')
                    .setColor(server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.COLOR || '#eb4034')
                    .setTimestamp();
                    
                if (server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.LARGE_IMAGE) {
                    embed.setImage(server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.LARGE_IMAGE);
                }
                
                if (server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.FOOTER) {
                    embed.setFooter({ text: server.SERVER_ONLINE_OFFLINE.OFFLINE_EMBED_SETTINGS.FOOTER });
                }
                
                hook.send({ embeds: [embed] })
                    .then(() => console.log(`[${server.SERVER_SHORTNAME}] Offline notification sent successfully`))
                    .catch(err => {
                        console.error(`[${server.SERVER_SHORTNAME}] Error sending offline notification: ${err.message}`);
                        logWebhookError(server, err);
                    });
            }
        } catch (err) {
            console.error(`[${server.SERVER_SHORTNAME}] Error handling offline message: ${err.message}`);
            logWebhookError(server, err);
        }
    } catch (err) {
        console.error(`[${server.SERVER_SHORTNAME}] Error handling offline message: ${err.message}`);
    }
}















