using System;
using System.Collections.Generic;
using System.Linq; // Add this for the Take() extension method
using Oxide.Core;
using Oxide.Core.Plugins;
using UnityEngine;
using Newtonsoft.Json;

namespace Oxide.Plugins
{
    [Info("Pickup Plus", "Frizzo420", "1.0.0")]
    [Description("Allows players to pick up large furnaces and water catchers")]
    class PickupPlus : RustPlugin
    {
        #region Configuration
        
        private Configuration config;
        
        class Configuration
        {
            [JsonProperty("Pickup Range")]
            public float PickupRange = 8f;
            
            [JsonProperty("Pickup Cooldown (seconds)")]
            public float PickupCooldown = 0f;
            
            [JsonProperty("Require Building Privilege")]
            public bool RequireBuildingPrivilege = true;
            
            [JsonProperty("Require Empty Container")]
            public bool RequireEmptyContainer = true;
            
            [JsonProperty("Refund Percentage")]
            public float RefundPercentage = 100f;
            
            [JsonProperty("Pickup Time Limit (minutes, 0 = unlimited)")]
            public float PickupTimeLimit = 30f;
            
            [JsonProperty("Pickupable Entities")]
            public Dictionary<string, PickupableEntityConfig> PickupableEntities = new Dictionary<string, PickupableEntityConfig>
            {
                ["large_furnace"] = new PickupableEntityConfig 
                { 
                    Enabled = true, 
                    Permission = "pickupplus.furnace",
                    CustomRefundPercentage = 100f
                },
                ["furnace.large"] = new PickupableEntityConfig 
                { 
                    Enabled = true, 
                    Permission = "pickupplus.furnace",
                    CustomRefundPercentage = 100f
                },
                ["water_catcher_small"] = new PickupableEntityConfig 
                { 
                    Enabled = true, 
                    Permission = "pickupplus.watercatcher.small",
                    CustomRefundPercentage = 100f
                },
                ["water_catcher_large"] = new PickupableEntityConfig 
                { 
                    Enabled = true, 
                    Permission = "pickupplus.watercatcher.large",
                    CustomRefundPercentage = 100f
                },
                ["cupboard.tool"] = new PickupableEntityConfig 
                { 
                    Enabled = true, 
                    Permission = "pickupplus.toolcupboard",
                    CustomRefundPercentage = 100f
                },
                ["tool.cupboard"] = new PickupableEntityConfig 
                { 
                    Enabled = true, 
                    Permission = "pickupplus.toolcupboard",
                    CustomRefundPercentage = 100f
                },
                ["cupboard.tool.deployed"] = new PickupableEntityConfig 
                { 
                    Enabled = true, 
                    Permission = "pickupplus.toolcupboard",
                    CustomRefundPercentage = 100f
                },
                ["cupboard.tool.shockbyte.deployed"] = new PickupableEntityConfig 
                { 
                    Enabled = true, 
                    Permission = "pickupplus.toolcupboard",
                    CustomRefundPercentage = 100f
                },
                ["cupboard.tool.retro.deployed"] = new PickupableEntityConfig 
                { 
                    Enabled = true, 
                    Permission = "pickupplus.toolcupboard",
                    CustomRefundPercentage = 100f
                },
                ["refinery_small"] = new PickupableEntityConfig 
                { 
                    Enabled = true, 
                    Permission = "pickupplus.refinery",
                    CustomRefundPercentage = 100f
                },
                ["refinery_small_deployed"] = new PickupableEntityConfig 
                { 
                    Enabled = true, 
                    Permission = "pickupplus.refinery",
                    CustomRefundPercentage = 100f
                }
            };
            
            [JsonProperty("Messages")]
            public MessageConfig Messages = new MessageConfig();
        }
        
        class PickupableEntityConfig
        {
            [JsonProperty("Enabled")]
            public bool Enabled = true;
            
            [JsonProperty("Permission")]
            public string Permission = "";
            
            [JsonProperty("Custom Refund Percentage")]
            public float CustomRefundPercentage = -1f;
        }
        
        class MessageConfig
        {
            [JsonProperty("Pickup Success")]
            public string PickupSuccess = "You have picked up {0}";
            
            [JsonProperty("No Permission")]
            public string NoPermission = "You don't have permission to pick up this item";
            
            [JsonProperty("No Building Privilege")]
            public string NoBuildingPrivilege = "You need building privilege to pick up this item";
            
            [JsonProperty("Container Not Empty")]
            public string ContainerNotEmpty = "The container must be empty before picking it up";
            
            [JsonProperty("Pickup Cooldown")]
            public string PickupCooldown = "You need to wait {0} seconds before picking up another item";
            
            [JsonProperty("Pickup Time Expired")]
            public string PickupTimeExpired = "This item can no longer be picked up";
            
            [JsonProperty("Too Far")]
            public string TooFar = "You are too far away to pick up this item";
        }
        
        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) throw new Exception();
                SaveConfig();
            }
            catch
            {
                PrintError("Your configuration file contains an error. Using default configuration values.");
                LoadDefaultConfig();
            }
        }

        protected override void LoadDefaultConfig() => config = new Configuration();

        protected override void SaveConfig() => Config.WriteObject(config);
        
        #endregion
        
        #region Data
        
        private Dictionary<ulong, EntityData> entityData = new Dictionary<ulong, EntityData>();
        private Dictionary<ulong, float> playerCooldowns = new Dictionary<ulong, float>();
        
        class EntityData
        {
            public ulong OwnerId;
            public float PlacementTime;
        }
        
        private void SaveData() => Interface.Oxide.DataFileSystem.WriteObject(Name, entityData);
        
        private void LoadData()
        {
            try
            {
                entityData = Interface.Oxide.DataFileSystem.ReadObject<Dictionary<ulong, EntityData>>(Name);
            }
            catch
            {
                entityData = new Dictionary<ulong, EntityData>();
            }
        }
        
        #endregion
        
        #region Hooks
        
        void OnServerInitialized()
        {
            // Register permissions
            permission.RegisterPermission("pickupplus.admin", this);
            permission.RegisterPermission("pickupplus.furnace", this);
            permission.RegisterPermission("pickupplus.watercatcher.small", this);
            permission.RegisterPermission("pickupplus.watercatcher.large", this);
            permission.RegisterPermission("pickupplus.toolcupboard", this);
            permission.RegisterPermission("pickupplus.refinery", this);
            
            // Load data
            LoadData();
            
            // Clean up old entity data
            if (config.PickupTimeLimit > 0)
            {
                CleanupOldEntityData();
            }
        }
        
        void Unload()
        {
            SaveData();
        }
        
        void OnEntityBuilt(Planner plan, GameObject go)
        {
            if (go == null) return;
            
            BaseEntity entity = go.GetComponent<BaseEntity>();
            if (entity == null) return;
            
            BasePlayer player = plan.GetOwnerPlayer();
            if (player == null) return;
            
            string prefabName = entity.ShortPrefabName;
            
            // Check if this entity is in our pickupable list
            if (config.PickupableEntities.ContainsKey(prefabName))
            {
                // Store the entity data
                entityData[entity.net.ID.Value] = new EntityData
                {
                    OwnerId = player.userID,
                    PlacementTime = Time.realtimeSinceStartup
                };
                
                SaveData();
            }
            
            // Handle special tool cupboard variants
            if (prefabName == "cupboard.tool.deployed" || prefabName == "tool.cupboard")
            {
                Item activeItem = player.GetActiveItem();
                if (activeItem != null)
                {
                    if (activeItem.name == "cupboard.tool.shockbyte.deployed")
                    {
                        // Replace with shockbyte variant
                        BaseEntity shockbyteTC = GameManager.server.CreateEntity("assets/prefabs/deployable/toolcupboard/shockbyte/cupboard.tool.shockbyte.deployed.prefab", entity.transform.position, entity.transform.rotation);
                        if (shockbyteTC != null)
                        {
                            shockbyteTC.Spawn();
                            entity.Kill();
                        }
                    }
                    else if (activeItem.name == "cupboard.tool.retro.deployed")
                    {
                        // Replace with retro variant
                        BaseEntity retroTC = GameManager.server.CreateEntity("assets/prefabs/deployable/toolcupboard/retro/cupboard.tool.retro.deployed.prefab", entity.transform.position, entity.transform.rotation);
                        if (retroTC != null)
                        {
                            retroTC.Spawn();
                            entity.Kill();
                        }
                    }
                }
            }
        }
        
        object CanBuild(Planner planner, Construction prefab, Construction.Target target)
        {
            BasePlayer player = planner?.GetOwnerPlayer();
            if (player == null) return null;
            
            Item activeItem = player.GetActiveItem();
            if (activeItem == null) return null;
            
            // Check if this is a tool cupboard with a special variant name
            if (activeItem.info.shortname == "tool.cupboard" || activeItem.info.itemid == -97956382)
            {
                if (activeItem.name == "cupboard.tool.shockbyte.deployed")
                {
                    // We'll handle this in OnEntityBuilt by checking the item name
                    Puts($"Player {player.displayName} is deploying a Shockbyte Tool Cupboard");
                }
                else if (activeItem.name == "cupboard.tool.retro.deployed")
                {
                    // We'll handle this in OnEntityBuilt by checking the item name
                    Puts($"Player {player.displayName} is deploying a Retro Tool Cupboard");
                }
            }
            
            return null;
        }
        
        void OnItemDeployed(Deployer deployer, BaseEntity entity)
        {
            if (entity == null || deployer == null) return;
            
            BasePlayer player = deployer.GetOwnerPlayer();
            if (player == null) return;
            
            Item activeItem = player.GetActiveItem();
            if (activeItem == null) return;
            
            // Check if this is a tool cupboard with a special variant name
            if (entity.ShortPrefabName == "cupboard.tool.deployed" || entity.ShortPrefabName == "tool.cupboard")
            {
                Puts($"Deployed tool cupboard with item name: {activeItem.name}");
                
                if (activeItem.name == "cupboard.tool.shockbyte.deployed")
                {
                    // Replace with shockbyte variant
                    Puts("Replacing with Shockbyte variant");
                    BaseEntity shockbyteTC = GameManager.server.CreateEntity("assets/prefabs/deployable/toolcupboard/shockbyte/cupboard.tool.shockbyte.deployed.prefab", entity.transform.position, entity.transform.rotation);
                    if (shockbyteTC != null)
                    {
                        shockbyteTC.Spawn();
                        entity.Kill();
                    }
                }
                else if (activeItem.name == "cupboard.tool.retro.deployed")
                {
                    // Replace with retro variant
                    Puts("Replacing with Retro variant");
                    BaseEntity retroTC = GameManager.server.CreateEntity("assets/prefabs/deployable/toolcupboard/retro/cupboard.tool.retro.deployed.prefab", entity.transform.position, entity.transform.rotation);
                    if (retroTC != null)
                    {
                        retroTC.Spawn();
                        entity.Kill();
                    }
                }
            }
        }
        
        #endregion
        
        #region Commands
        
        [ChatCommand("pickup")]
        void PickupCommand(BasePlayer player, string command, string[] args)
        {
            // Check cooldown
            if (playerCooldowns.ContainsKey(player.userID))
            {
                float timeLeft = playerCooldowns[player.userID] - Time.realtimeSinceStartup;
                if (timeLeft > 0)
                {
                    SendMessage(player, string.Format(config.Messages.PickupCooldown, Math.Round(timeLeft)));
                    return;
                }
                playerCooldowns.Remove(player.userID);
            }
            
            // Find entity player is looking at
            RaycastHit hit;
            if (!Physics.Raycast(player.eyes.HeadRay(), out hit, config.PickupRange, LayerMask.GetMask("Deployed", "Construction", "Default", "Deployable", "Prevent Building", "Deployed")))
            {
                SendMessage(player, config.Messages.TooFar);
                return;
            }
            
            BaseEntity entity = hit.GetEntity();
            if (entity == null) 
            {
                SendMessage(player, "No entity found. Try looking directly at the item.");
                return;
            }
            
            string prefabName = entity.ShortPrefabName;
            
            // Debug info
            Puts($"Player {player.displayName} attempting to pick up: {prefabName}");
            
            // Check if entity is pickupable
            if (!config.PickupableEntities.ContainsKey(prefabName) || !config.PickupableEntities[prefabName].Enabled)
            {
                SendMessage(player, $"This item ({prefabName}) cannot be picked up.");
                return;
            }
            
            // Check permission
            string permissionName = config.PickupableEntities[prefabName].Permission;
            if (!string.IsNullOrEmpty(permissionName) && !HasPermission(player, permissionName) && !HasPermission(player, "pickupplus.admin"))
            {
                SendMessage(player, config.Messages.NoPermission);
                return;
            }
            
            // Check building privilege
            if (config.RequireBuildingPrivilege && !player.CanBuild())
            {
                SendMessage(player, config.Messages.NoBuildingPrivilege);
                return;
            }
            
            // Check if container is empty
            if (config.RequireEmptyContainer)
            {
                StorageContainer container = entity as StorageContainer;
                if (container != null && container.inventory.itemList.Count > 0)
                {
                    SendMessage(player, config.Messages.ContainerNotEmpty);
                    return;
                }
            }
            
            // Check time limit
            if (config.PickupTimeLimit > 0 && entityData.ContainsKey(entity.net.ID.Value))
            {
                float timeSincePlacement = Time.realtimeSinceStartup - entityData[entity.net.ID.Value].PlacementTime;
                if (timeSincePlacement > config.PickupTimeLimit * 60f)
                {
                    SendMessage(player, config.Messages.PickupTimeExpired);
                    return;
                }
            }
            
            // Get refund percentage
            float refundPercentage = config.RefundPercentage;
            if (config.PickupableEntities[prefabName].CustomRefundPercentage >= 0)
                refundPercentage = config.PickupableEntities[prefabName].CustomRefundPercentage;
                
            // Give item to player
            Item item = null;

            // Debug info
            Puts($"Creating item for entity: {prefabName}");

            // Create the correct item based on the entity type
            switch (prefabName)
            {
                case "large_furnace":
                case "furnace.large":
                    item = ItemManager.CreateByName("furnace.large", 1, entity.skinID);
                    break;
                case "water_catcher_small":
                    item = ItemManager.CreateByName("water.catcher.small", 1, entity.skinID);
                    break;
                case "water_catcher_large":
                    item = ItemManager.CreateByName("water.catcher.large", 1, entity.skinID);
                    break;
                case "cupboard.tool":
                case "tool.cupboard":
                case "cupboard.tool.deployed":
                    item = ItemManager.CreateByItemID(-97956382, 1, entity.skinID);
                    break;
                case "cupboard.tool.shockbyte.deployed":
                    // Create the correct Shockbyte Tool Cupboard item
                    item = ItemManager.CreateByItemID(1174957864, 1, entity.skinID);
                    Puts($"Created Shockbyte Tool Cupboard with ID: 1174957864");
                    break;
                case "cupboard.tool.retro.deployed":
                    // Create the correct Retro Tool Cupboard item
                    item = ItemManager.CreateByItemID(1488606552, 1, entity.skinID);
                    Puts($"Created Retro Tool Cupboard with ID: 1488606552");
                    break;
                case "refinery_small":
                case "refinery_small_deployed":
                    item = ItemManager.CreateByItemID(-1293296287, 1, entity.skinID); // Small Refinery item ID
                    break;
                default:
                    // Fallback to using the prefab name directly
                    item = ItemManager.CreateByName(entity.ShortPrefabName, 1, entity.skinID);
                    break;
            }

            if (item != null)
            {
                Puts($"Successfully created item: {item.info.displayName.english}");
                
                // Give the item to the player
                if (player.inventory.GiveItem(item))
                {
                    SendMessage(player, string.Format(config.Messages.PickupSuccess, item.info.displayName.english));
                }
                else
                {
                    // If inventory is full, drop the item at the player's feet
                    item.Drop(player.transform.position + new Vector3(0, 1, 0), Vector3.zero);
                    SendMessage(player, $"Your inventory is full. {item.info.displayName.english} has been dropped at your feet.");
                }
            }
            else
            {
                SendMessage(player, $"Failed to create item for {prefabName}. Please report this to an admin.");
                return;
            }
            
            // Set cooldown
            if (config.PickupCooldown > 0)
                playerCooldowns[player.userID] = Time.realtimeSinceStartup + config.PickupCooldown;
                
            // Remove entity data
            if (entityData.ContainsKey(entity.net.ID.Value))
                entityData.Remove(entity.net.ID.Value);
                
            // Kill entity
            entity.Kill();
            
            SaveData();
        }
        
        [ChatCommand("checkentity")]
        void CheckEntityCommand(BasePlayer player, string command, string[] args)
        {
            RaycastHit hit;
            if (!Physics.Raycast(player.eyes.HeadRay(), out hit, 5f, LayerMask.GetMask("Deployed", "Construction", "Default", "Deployable", "Prevent Building", "Deployed")))
            {
                SendMessage(player, "No entity found. Try looking directly at the item.");
                return;
            }
            
            BaseEntity entity = hit.GetEntity();
            if (entity == null)
            {
                SendMessage(player, "No entity found.");
                return;
            }
            
            string prefabName = entity.ShortPrefabName;
            string fullPrefabName = entity.PrefabName;
            
            SendMessage(player, $"Entity: {prefabName}\nFull path: {fullPrefabName}\nEntity ID: {entity.net.ID}\nSkin ID: {entity.skinID}");
        }
        
        [ChatCommand("finditem")]
        void FindItemCommand(BasePlayer player, string command, string[] args)
        {
            if (args.Length == 0)
            {
                SendMessage(player, "Usage: /finditem <item name>");
                return;
            }
            
            string searchTerm = string.Join(" ", args).ToLower();
            List<string> results = new List<string>();
            
            foreach (var item in ItemManager.itemList)
            {
                if (item.shortname.ToLower().Contains(searchTerm) || 
                    item.displayName.english.ToLower().Contains(searchTerm))
                {
                    results.Add($"Item: {item.displayName.english}, Shortname: {item.shortname}, ID: {item.itemid}");
                }
            }
            
            if (results.Count == 0)
            {
                SendMessage(player, $"No items found matching '{searchTerm}'");
            }
            else
            {
                SendMessage(player, $"Found {results.Count} items matching '{searchTerm}':");
                int count = 0;
                foreach (var result in results)
                {
                    if (count < 10)
                    {
                        SendMessage(player, result);
                        count++;
                    }
                    else
                    {
                        break;
                    }
                }
                
                if (results.Count > 10)
                {
                    SendMessage(player, $"...and {results.Count - 10} more results.");
                }
            }
        }
        
        #endregion
        
        #region Helper Methods
        
        private void SendMessage(BasePlayer player, string message)
        {
            player.ChatMessage(message);
        }
        
        private bool HasPermission(BasePlayer player, string perm)
        {
            return permission.UserHasPermission(player.UserIDString, perm);
        }
        
        private void CleanupOldEntityData()
        {
            float currentTime = Time.realtimeSinceStartup;
            float timeLimit = config.PickupTimeLimit * 60f;
            
            List<ulong> keysToRemove = new List<ulong>();
            
            foreach (var entry in entityData)
            {
                if (currentTime - entry.Value.PlacementTime > timeLimit)
                {
                    keysToRemove.Add(entry.Key);
                }
            }
            
            foreach (var key in keysToRemove)
            {
                entityData.Remove(key);
            }
            
            if (keysToRemove.Count > 0)
            {
                SaveData();
            }
        }
        
        #endregion
    }
}




